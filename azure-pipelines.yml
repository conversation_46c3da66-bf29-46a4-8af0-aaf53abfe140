# Adapted from https://github.com/numba/numba/blob/master/azure-pipelines.yml
trigger:
  branches:
    include:
    - main
    - 1.4.x
  paths:
    exclude:
    - 'doc/*'

pr:
  autoCancel: true
  branches:
    include:
    - main
    - 1.4.x

variables:
  PYTEST_WORKERS: auto
  PYTEST_TARGET:  pandas
  PATTERN: "not slow and not high_memory and not db and not network and not single_cpu"
  PANDAS_CI: 1

jobs:
- template: ci/azure/posix.yml
  parameters:
    name: macOS
    vmImage: macOS-10.15

- template: ci/azure/windows.yml
  parameters:
    name: Windows
    vmImage: windows-2019

- job: py38_32bit
  pool:
    vmImage: ubuntu-18.04

  steps:
    # TODO: GH#44980 https://github.com/pypa/setuptools/issues/2941
    - script: |
        docker pull quay.io/pypa/manylinux2014_i686
        docker run -v $(pwd):/pandas quay.io/pypa/manylinux2014_i686 \
        /bin/bash -xc "cd pandas && \
        /opt/python/cp38-cp38/bin/python -m venv ~/virtualenvs/pandas-dev && \
        . ~/virtualenvs/pandas-dev/bin/activate && \
        python -m pip install --no-deps -U pip wheel 'setuptools<60.0.0' && \
        pip install cython numpy python-dateutil pytz pytest pytest-xdist pytest-asyncio>=0.17 hypothesis && \
        python setup.py build_ext -q -j2 && \
        python -m pip install --no-build-isolation -e . && \
        pytest -m 'not slow and not network and not clipboard' pandas --junitxml=test-data.xml"
      displayName: 'Run 32-bit manylinux2014 Docker Build / Tests'

    - task: PublishTestResults@2
      condition: succeededOrFailed()
      inputs:
        testResultsFiles: '**/test-*.xml'
        failTaskOnFailedTests: true
        testRunTitle: 'Publish test results for Python 3.8-32 bit full Linux'
