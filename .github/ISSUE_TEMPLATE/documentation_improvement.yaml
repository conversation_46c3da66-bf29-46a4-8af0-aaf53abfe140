name: Documentation Improvement
description: Report wrong or missing documentation
title: "DOC: "
labels: [Docs, Needs Triage]

body:
  - type: checkboxes
    attributes:
      label: Pandas version checks
      options:
        - label: >
            I have checked that the issue still exists on the latest versions of the docs
            on `main` [here](https://pandas.pydata.org/docs/dev/)
          required: true
  - type: textarea
    id: location
    attributes:
      label: Location of the documentation
      description: >
        Please provide the location of the documentation, e.g. "pandas.read_csv" or the
        URL of the documentation, e.g.
        "https://pandas.pydata.org/docs/reference/api/pandas.read_csv.html"
      placeholder: https://pandas.pydata.org/docs/reference/api/pandas.read_csv.html
    validations:
      required: true
  - type: textarea
    id: problem
    attributes:
      label: Documentation problem
      description: >
        Please provide a description of what documentation you believe needs to be fixed/improved
    validations:
      required: true
  - type: textarea
    id: suggested-fix
    attributes:
      label: Suggested fix for documentation
      description: >
        Please explain the suggested fix and **why** it's better than the existing documentation
    validations:
      required: true
