name: Comment-bot

on:
  issue_comment:
    types:
      - created
      - edited

jobs:
  autotune:
    name: "Fixup pre-commit formatting"
    if: startsWith(github.event.comment.body, '@github-actions pre-commit')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: r-lib/actions/pr-fetch@v2
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Cache multiple paths
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/pre-commit
            ~/.cache/pip
          key: pre-commit-dispatched-${{ runner.os }}-build
      - uses: actions/setup-python@v3
        with:
          python-version: 3.8
      - name: Install-pre-commit
        run: python -m pip install --upgrade pre-commit
      - name: Run pre-commit
        run: pre-commit run --from-ref=origin/main --to-ref=HEAD --all-files || (exit 0)
      - name: Commit results
        run: |
          git config user.name "$(git log -1 --pretty=format:%an)"
          git config user.email "$(git log -1 --pretty=format:%ae)"
          git commit -a -m 'Fixes from pre-commit [automated commit]' || echo "No changes to commit"
      - uses: r-lib/actions/pr-push@v2
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
