name: "ASV Bot"

on:
  issue_comment: # Pull requests are issues
    types:
      - created

env:
  ENV_FILE: environment.yml
  COMMENT: ${{github.event.comment.body}}

jobs:
  autotune:
    name: "Run benchmarks"
    # TODO: Support more benchmarking options later, against different branches, against self, etc
    if: startsWith(github.event.comment.body, '@github-actions benchmark')
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash -el {0}

    concurrency:
      # Set concurrency to prevent abuse(full runs are ~5.5 hours !!!)
      # each user can only run one concurrent benchmark bot at a time
      # We don't cancel in progress jobs, but if you want to benchmark multiple PRs, you're gonna have
      # to wait
      group: ${{ github.actor }}-asv
      cancel-in-progress: false

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Cache conda
        uses: actions/cache@v3
        with:
          path: ~/conda_pkgs_dir
          key: ${{ runner.os }}-conda-${{ hashFiles('${{ env.ENV_FILE }}') }}

        # Although asv sets up its own env, deps are still needed
        # during discovery process
      - uses: conda-incubator/setup-miniconda@v2.1.1
        with:
          activate-environment: pandas-dev
          channel-priority: strict
          environment-file: ${{ env.ENV_FILE }}
          use-only-tar-bz2: true

      - name: Run benchmarks
        id: bench
        continue-on-error: true # This is a fake failure, asv will exit code 1 for regressions
        run: |
          # extracting the regex, see https://stackoverflow.com/a/36798723
          REGEX=$(echo "$COMMENT" | sed -n "s/^.*-b\s*\(\S*\).*$/\1/p")
          cd asv_bench
          asv check -E existing
          git remote add upstream https://github.com/pandas-dev/pandas.git
          git fetch upstream
          asv machine --yes
          asv continuous -f 1.1 -b $REGEX upstream/main HEAD
          echo 'BENCH_OUTPUT<<EOF' >> $GITHUB_ENV
          asv compare -f 1.1 upstream/main HEAD >> $GITHUB_ENV
          echo 'EOF' >> $GITHUB_ENV
          echo "REGEX=$REGEX" >> $GITHUB_ENV

      - uses: actions/github-script@v6
        env:
          BENCH_OUTPUT: ${{env.BENCH_OUTPUT}}
          REGEX: ${{env.REGEX}}
        with:
          script: |
            const ENV_VARS = process.env
            const run_url = `https://github.com/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId}`
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '\nBenchmarks completed. View runner logs here.' + run_url + '\nRegex used: '+ 'regex ' + ENV_VARS["REGEX"] + '\n' + ENV_VARS["BENCH_OUTPUT"]
            })
