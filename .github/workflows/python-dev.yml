# This file is purposely frozen(does not run). DO NOT DELETE IT
# Unfreeze(by commentingthe if: false() condition) once the
# next Python Dev version has released beta 1 and both Cython and numpy support it
# After that Python has released, migrate the workflows to the
# posix GHA workflows/Azure pipelines and "freeze" this file by
# uncommenting the if: false() condition
# Feel free to modify this comment as necessary.

name: Python Dev

on:
  push:
    branches:
      - main
      - 1.4.x
  pull_request:
    branches:
      - main
      - 1.4.x
    paths-ignore:
      - "doc/**"

env:
  PYTEST_WORKERS: "auto"
  PANDAS_CI: 1
  PATTERN: "not slow and not network and not clipboard and not single_cpu"
  COVERAGE: true
  PYTEST_TARGET: pandas

jobs:
  build:
    if: false # Comment this line out to "unfreeze"
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macOS-latest, windows-latest]

    name: actions-311-dev
    timeout-minutes: 80

    concurrency:
      #https://github.community/t/concurrecy-not-work-for-push/183068/7
      group: ${{ github.event_name == 'push' && github.run_number || github.ref }}-${{ matrix.os }}-${{ matrix.pytest_target }}-dev
      cancel-in-progress: true

    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0

    - name: Set up Python Dev Version
      uses: actions/setup-python@v3
      with:
        python-version: '3.11-dev'

    # TODO: GH#44980 https://github.com/pypa/setuptools/issues/2941
    - name: Install dependencies
      shell: bash -el {0}
      run: |
        python -m pip install --upgrade pip "setuptools<60.0.0" wheel
        pip install -i https://pypi.anaconda.org/scipy-wheels-nightly/simple numpy
        pip install git+https://github.com/nedbat/coveragepy.git
        pip install cython python-dateutil pytz hypothesis pytest>=6.2.5 pytest-xdist pytest-cov
        pip list

    - name: Build Pandas
      run: |
        python setup.py build_ext -q -j2
        python -m pip install -e . --no-build-isolation --no-use-pep517

    - name: Build Version
      run: |
        python -c "import pandas; pandas.show_versions();"

    - name: Test with pytest
      shell: bash -el {0}
      run: |
        ci/run_tests.sh

    - name: Publish test results
      uses: actions/upload-artifact@v3
      with:
        name: Test results
        path: test-data.xml
      if: failure()

    - name: Report Coverage
      run: |
        coverage report -m

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v2
      with:
        flags: unittests
        name: codecov-pandas
        fail_ci_if_error: true
