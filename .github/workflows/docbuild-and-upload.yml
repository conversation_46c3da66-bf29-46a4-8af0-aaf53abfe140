name: Doc Build and Upload

on:
  push:
    branches:
      - main
      - 1.4.x
  pull_request:
    branches:
      - main
      - 1.4.x

env:
  ENV_FILE: environment.yml
  PANDAS_CI: 1

jobs:
  web_and_docs:
    name: Doc Build and Upload
    runs-on: ubuntu-latest

    concurrency:
      # https://github.community/t/concurrecy-not-work-for-push/183068/7
      group: ${{ github.event_name == 'push' && github.run_number || github.ref }}-web-docs
      cancel-in-progress: true

    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        fetch-depth: 0

    - name: Set up pandas
      uses: ./.github/actions/setup

    - name: Build website
      run: |
        source activate pandas-dev
        python web/pandas_web.py web/pandas --target-path=web/build
    - name: Build documentation
      run: |
        source activate pandas-dev
        doc/make.py --warnings-are-errors

    - name: Install ssh key
      run: |
        mkdir -m 700 -p ~/.ssh
        echo "${{ secrets.server_ssh_key }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        echo "${{ secrets.server_ip }} ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBE1Kkopomm7FHG5enATf7SgnpICZ4W2bw+Ho+afqin+w7sMcrsa0je7sbztFAV8YchDkiBKnWTG4cRT+KZgZCaY=" > ~/.ssh/known_hosts
      if: ${{github.event_name == 'push' && github.ref == 'refs/heads/main'}}

    - name: Copy cheatsheets into site directory
      run: cp doc/cheatsheet/Pandas_Cheat_Sheet* web/build/

    - name: Upload web
      run: rsync -az --delete --exclude='pandas-docs' --exclude='docs' web/build/ docs@${{ secrets.server_ip }}:/usr/share/nginx/pandas
      if: ${{github.event_name == 'push' && github.ref == 'refs/heads/main'}}

    - name: Upload dev docs
      run: rsync -az --delete doc/build/html/ docs@${{ secrets.server_ip }}:/usr/share/nginx/pandas/pandas-docs/dev
      if: ${{github.event_name == 'push' && github.ref == 'refs/heads/main'}}

    - name: Move docs into site directory
      run: mv doc/build/html web/build/docs

    - name: Save website as an artifact
      uses: actions/upload-artifact@v3
      with:
        name: website
        path: web/build
        retention-days: 14
