name: Assign
on:
  issue_comment:
    types: created

jobs:
  issue_assign:
    runs-on: ubuntu-latest
    steps:
    - if: github.event.comment.body == 'take'
      run: |
        echo "Assigning issue ${{ github.event.issue.number }} to ${{ github.event.comment.user.login }}"
        curl -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" -d '{"assignees": ["${{ github.event.comment.user.login }}"]}' https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.issue.number }}/assignees
