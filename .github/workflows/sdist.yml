name: sdist

on:
  push:
    branches:
      - main
      - 1.4.x
  pull_request:
    branches:
      - main
      - 1.4.x
    paths-ignore:
      - "doc/**"

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    defaults:
      run:
        shell: bash -el {0}

    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.8", "3.9", "3.10"]
    concurrency:
      # https://github.community/t/concurrecy-not-work-for-push/183068/7
      group: ${{ github.event_name == 'push' && github.run_number || github.ref }}-${{matrix.python-version}}-sdist
      cancel-in-progress: true

    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}

    # TODO: GH#44980 https://github.com/pypa/setuptools/issues/2941
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip "setuptools<60.0.0" wheel

        # GH 39416
        pip install numpy

    - name: Build pandas sdist
      run: |
        pip list
        python setup.py sdist --formats=gztar

    - name: Upload sdist artifact
      uses: actions/upload-artifact@v3
      with:
        name: ${{matrix.python-version}}-sdist.gz
        path: dist/*.gz

    - uses: conda-incubator/setup-miniconda@v2.1.1
      with:
        activate-environment: pandas-sdist
        channels: conda-forge
        python-version: '${{ matrix.python-version }}'

    # TODO: GH#44980 https://github.com/pypa/setuptools/issues/2941
    - name: Install pandas from sdist
      run: |
        python -m pip install --upgrade "setuptools<60.0.0"
        pip list
        python -m pip install dist/*.gz

    - name: Force oldest supported NumPy
      run: |
        case "${{matrix.python-version}}" in
        3.8)
          pip install numpy==1.18.5 ;;
        3.9)
          pip install numpy==1.19.3 ;;
        3.10)
          pip install numpy==1.21.2 ;;
        esac

    - name: Import pandas
      run: |
        cd ..
        conda list
        python -c "import pandas; pandas.show_versions();"
