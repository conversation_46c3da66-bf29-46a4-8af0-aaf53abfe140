<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sankuai.meishi.stgy.algoplatform</groupId>
        <artifactId>algoplat-predictor</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>predictor-service</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    <name>predictor-service</name>
    <dependencies>
        <dependency>
            <groupId>com.sankuai.nib.data</groupId>
            <artifactId>zb-datatrace-platform-sdk</artifactId>
            <version>1.0.14</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meishi.stgy.algoplatform</groupId>
            <artifactId>predictor-client</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-pigeon</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-crane</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sf.py4j</groupId>
            <artifactId>py4j</artifactId>
            <version>********</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jgit</groupId>
            <artifactId>org.eclipse.jgit</artifactId>
            <version>5.4.0.201906121030-r</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>2.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.hadoop.afo</groupId>
            <artifactId>afo-serving-client-commons</artifactId>
            <version>0.6.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-redis-squirrel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-cluster-limiter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mlp</groupId>
            <artifactId>feature-idl</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-cellar</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.service</groupId>
            <artifactId>horizon-server-api</artifactId>
            <version>0.1.0.093</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.service</groupId>
            <artifactId>horizon-rhino-sdk</artifactId>
            <version>0.1.0.093</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.ut.toolkit</groupId>
            <artifactId>runtime</artifactId>
            <version>1.0.10</version>
            <scope>test</scope>
        </dependency>
        <!--mockito-->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <!-- mlp xgb-->
        <dependency>
            <groupId>com.meituan.hadoop.afo.jpmmlclient</groupId>
            <artifactId>jpmmlPredictClient</artifactId>
            <version>0.62</version>
        </dependency>
        <!-- mlp TRITON-->
        <dependency>
            <groupId>com.meituan.hadoop.afo</groupId>
            <artifactId>afo-serving-triton</artifactId>
            <version>0.0.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>grpc-core</artifactId>
                    <groupId>io.grpc</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.12.0</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-mafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-metrics</artifactId>
        </dependency>
        <!-- appserver 服务 -->
        <dependency>
            <groupId>com.sankuai.meishi.stgy.algoplatform</groupId>
            <artifactId>app-client</artifactId>
            <version>0.0.11</version>
        </dependency>
        <!-- msgpack -->
        <dependency>
            <groupId>org.msgpack</groupId>
            <artifactId>msgpack-core</artifactId>
            <version>0.9.3</version>
        </dependency>
        <dependency>
            <groupId>martgeneral-infer</groupId>
            <artifactId>martgeneral-infer-api</artifactId>
            <version>1.1.4</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>mss-java-sdk-s3</artifactId>
        </dependency>
        <!-- 大模型预测服务 -->
        <dependency>
            <groupId>com.sankuai.algoplatform.llmpredict</groupId>
            <artifactId>predict-api</artifactId>
            <version>1.0.4</version>
        </dependency>
        <!-- PigeonSDK -->
        <dependency>
            <groupId>com.dianping.dpsf</groupId>
            <artifactId>dpsf-net</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.octo</groupId>
            <artifactId>dorado-mesh</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>test</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/profiles/test</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                    <resource>
                        <directory>src/test/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/profiles/prod</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>staging</id>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/profiles/staging</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>
</project>
