<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper.AlgoPackageMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoPackagePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="owner_mis" jdbcType="VARCHAR" property="ownerMis" />
    <result column="runtime" jdbcType="VARCHAR" property="runtime" />
    <result column="module_path" jdbcType="VARCHAR" property="modulePath" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="code_repo" jdbcType="VARCHAR" property="codeRepo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, note, owner_mis, runtime, module_path, version, code_repo, status, add_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.AlgoPackageExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from algo_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from algo_package
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from algo_package
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.AlgoPackageExample">
    delete from algo_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoPackagePo">
    insert into algo_package (id, note, owner_mis, 
      runtime, module_path, version, 
      code_repo, status, add_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{note,jdbcType=VARCHAR}, #{ownerMis,jdbcType=VARCHAR}, 
      #{runtime,jdbcType=VARCHAR}, #{modulePath,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, 
      #{codeRepo,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoPackagePo">
    insert into algo_package
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="ownerMis != null">
        owner_mis,
      </if>
      <if test="runtime != null">
        runtime,
      </if>
      <if test="modulePath != null">
        module_path,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="codeRepo != null">
        code_repo,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="ownerMis != null">
        #{ownerMis,jdbcType=VARCHAR},
      </if>
      <if test="runtime != null">
        #{runtime,jdbcType=VARCHAR},
      </if>
      <if test="modulePath != null">
        #{modulePath,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="codeRepo != null">
        #{codeRepo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.AlgoPackageExample" resultType="java.lang.Long">
    select count(*) from algo_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update algo_package
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.note != null">
        note = #{record.note,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerMis != null">
        owner_mis = #{record.ownerMis,jdbcType=VARCHAR},
      </if>
      <if test="record.runtime != null">
        runtime = #{record.runtime,jdbcType=VARCHAR},
      </if>
      <if test="record.modulePath != null">
        module_path = #{record.modulePath,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.codeRepo != null">
        code_repo = #{record.codeRepo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update algo_package
    set id = #{record.id,jdbcType=BIGINT},
      note = #{record.note,jdbcType=VARCHAR},
      owner_mis = #{record.ownerMis,jdbcType=VARCHAR},
      runtime = #{record.runtime,jdbcType=VARCHAR},
      module_path = #{record.modulePath,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      code_repo = #{record.codeRepo,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoPackagePo">
    update algo_package
    <set>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="ownerMis != null">
        owner_mis = #{ownerMis,jdbcType=VARCHAR},
      </if>
      <if test="runtime != null">
        runtime = #{runtime,jdbcType=VARCHAR},
      </if>
      <if test="modulePath != null">
        module_path = #{modulePath,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="codeRepo != null">
        code_repo = #{codeRepo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoPackagePo">
    update algo_package
    set note = #{note,jdbcType=VARCHAR},
      owner_mis = #{ownerMis,jdbcType=VARCHAR},
      runtime = #{runtime,jdbcType=VARCHAR},
      module_path = #{modulePath,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      code_repo = #{codeRepo,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into algo_package
    (id, note, owner_mis, runtime, module_path, version, code_repo, status, add_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.note,jdbcType=VARCHAR}, #{item.ownerMis,jdbcType=VARCHAR}, 
        #{item.runtime,jdbcType=VARCHAR}, #{item.modulePath,jdbcType=VARCHAR}, #{item.version,jdbcType=VARCHAR}, 
        #{item.codeRepo,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>