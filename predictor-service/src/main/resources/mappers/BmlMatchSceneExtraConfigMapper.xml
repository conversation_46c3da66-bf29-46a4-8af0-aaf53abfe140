<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper.BmlMatchSceneExtraConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BmlMatchSceneExtraConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="match_scene_id" jdbcType="BIGINT" property="matchSceneId" />
    <result column="data_mapping_config" jdbcType="CHAR" property="dataMappingConfig" />
    <result column="result_process_config" jdbcType="CHAR" property="resultProcessConfig" />
    <result column="deploy_info_config" jdbcType="CHAR" property="deployInfoConfig" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, match_scene_id, data_mapping_config, result_process_config, deploy_info_config, 
    description, owner, status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.BmlMatchSceneExtraConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bml_match_scene_extra_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bml_match_scene_extra_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bml_match_scene_extra_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.BmlMatchSceneExtraConfigExample">
    delete from bml_match_scene_extra_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BmlMatchSceneExtraConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bml_match_scene_extra_config (match_scene_id, data_mapping_config, result_process_config, 
      deploy_info_config, description, owner, 
      status, add_time, update_time
      )
    values (#{matchSceneId,jdbcType=BIGINT}, #{dataMappingConfig,jdbcType=CHAR}, #{resultProcessConfig,jdbcType=CHAR}, 
      #{deployInfoConfig,jdbcType=CHAR}, #{description,jdbcType=VARCHAR}, #{owner,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BmlMatchSceneExtraConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bml_match_scene_extra_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="matchSceneId != null">
        match_scene_id,
      </if>
      <if test="dataMappingConfig != null">
        data_mapping_config,
      </if>
      <if test="resultProcessConfig != null">
        result_process_config,
      </if>
      <if test="deployInfoConfig != null">
        deploy_info_config,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="owner != null">
        owner,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="matchSceneId != null">
        #{matchSceneId,jdbcType=BIGINT},
      </if>
      <if test="dataMappingConfig != null">
        #{dataMappingConfig,jdbcType=CHAR},
      </if>
      <if test="resultProcessConfig != null">
        #{resultProcessConfig,jdbcType=CHAR},
      </if>
      <if test="deployInfoConfig != null">
        #{deployInfoConfig,jdbcType=CHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.BmlMatchSceneExtraConfigExample" resultType="java.lang.Long">
    select count(*) from bml_match_scene_extra_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bml_match_scene_extra_config
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.matchSceneId != null">
        match_scene_id = #{row.matchSceneId,jdbcType=BIGINT},
      </if>
      <if test="row.dataMappingConfig != null">
        data_mapping_config = #{row.dataMappingConfig,jdbcType=CHAR},
      </if>
      <if test="row.resultProcessConfig != null">
        result_process_config = #{row.resultProcessConfig,jdbcType=CHAR},
      </if>
      <if test="row.deployInfoConfig != null">
        deploy_info_config = #{row.deployInfoConfig,jdbcType=CHAR},
      </if>
      <if test="row.description != null">
        description = #{row.description,jdbcType=VARCHAR},
      </if>
      <if test="row.owner != null">
        owner = #{row.owner,jdbcType=VARCHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=TINYINT},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bml_match_scene_extra_config
    set id = #{row.id,jdbcType=BIGINT},
      match_scene_id = #{row.matchSceneId,jdbcType=BIGINT},
      data_mapping_config = #{row.dataMappingConfig,jdbcType=CHAR},
      result_process_config = #{row.resultProcessConfig,jdbcType=CHAR},
      deploy_info_config = #{row.deployInfoConfig,jdbcType=CHAR},
      description = #{row.description,jdbcType=VARCHAR},
      owner = #{row.owner,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=TINYINT},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BmlMatchSceneExtraConfig">
    update bml_match_scene_extra_config
    <set>
      <if test="matchSceneId != null">
        match_scene_id = #{matchSceneId,jdbcType=BIGINT},
      </if>
      <if test="dataMappingConfig != null">
        data_mapping_config = #{dataMappingConfig,jdbcType=CHAR},
      </if>
      <if test="resultProcessConfig != null">
        result_process_config = #{resultProcessConfig,jdbcType=CHAR},
      </if>
      <if test="deployInfoConfig != null">
        deploy_info_config = #{deployInfoConfig,jdbcType=CHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BmlMatchSceneExtraConfig">
    update bml_match_scene_extra_config
    set match_scene_id = #{matchSceneId,jdbcType=BIGINT},
      data_mapping_config = #{dataMappingConfig,jdbcType=CHAR},
      result_process_config = #{resultProcessConfig,jdbcType=CHAR},
      deploy_info_config = #{deployInfoConfig,jdbcType=CHAR},
      description = #{description,jdbcType=VARCHAR},
      owner = #{owner,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into bml_match_scene_extra_config
    (match_scene_id, data_mapping_config, result_process_config, deploy_info_config, 
      description, owner, status, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.matchSceneId,jdbcType=BIGINT}, #{item.dataMappingConfig,jdbcType=CHAR}, #{item.resultProcessConfig,jdbcType=CHAR}, 
        #{item.deployInfoConfig,jdbcType=CHAR}, #{item.description,jdbcType=VARCHAR}, #{item.owner,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=TINYINT}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>