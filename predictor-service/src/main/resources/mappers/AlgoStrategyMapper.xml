<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper.AlgoStrategyMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoStrategyPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="package_id" jdbcType="BIGINT" property="packageId"/>
        <result column="entrance_path" jdbcType="VARCHAR" property="entrancePath"/>
        <result column="entrance_method" jdbcType="VARCHAR" property="entranceMethod"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , note, package_id, entrance_path, entrance_method, status, add_time,
    update_time
    </sql>
    <select id="selectByExample"
            parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.AlgoStrategyExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from algo_strategy
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from algo_strategy
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from algo_strategy
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample"
            parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.AlgoStrategyExample">
        delete from algo_strategy
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoStrategyPo">
        insert into algo_strategy (id, note, package_id,
                                   entrance_path, entrance_method,
                                   status, add_time, update_time)
        values (#{id,jdbcType=BIGINT}, #{note,jdbcType=VARCHAR}, #{packageId,jdbcType=BIGINT},
                #{entrancePath,jdbcType=VARCHAR}, #{entranceMethod,jdbcType=VARCHAR},
                #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective"
            parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoStrategyPo">
        insert into algo_strategy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="note != null">
                note,
            </if>
            <if test="packageId != null">
                package_id,
            </if>
            <if test="entrancePath != null">
                entrance_path,
            </if>
            <if test="entranceMethod != null">
                entrance_method,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="note != null">
                #{note,jdbcType=VARCHAR},
            </if>
            <if test="packageId != null">
                #{packageId,jdbcType=BIGINT},
            </if>
            <if test="entrancePath != null">
                #{entrancePath,jdbcType=VARCHAR},
            </if>
            <if test="entranceMethod != null">
                #{entranceMethod,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <select id="countByExample"
            parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.AlgoStrategyExample"
            resultType="java.lang.Long">
        select count(*) from algo_strategy
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update algo_strategy
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.note != null">
                note = #{record.note,jdbcType=VARCHAR},
            </if>
            <if test="record.packageId != null">
                package_id = #{record.packageId,jdbcType=BIGINT},
            </if>
            <if test="record.entrancePath != null">
                entrance_path = #{record.entrancePath,jdbcType=VARCHAR},
            </if>
            <if test="record.entranceMethod != null">
                entrance_method = #{record.entranceMethod,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=INTEGER},
            </if>
            <if test="record.addTime != null">
                add_time = #{record.addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update algo_strategy
        set id = #{record.id,jdbcType=BIGINT},
        note = #{record.note,jdbcType=VARCHAR},
        package_id = #{record.packageId,jdbcType=BIGINT},
        entrance_path = #{record.entrancePath,jdbcType=VARCHAR},
        entrance_method = #{record.entranceMethod,jdbcType=VARCHAR},
        status = #{record.status,jdbcType=INTEGER},
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoStrategyPo">
        update algo_strategy
        <set>
            <if test="note != null">
                note = #{note,jdbcType=VARCHAR},
            </if>
            <if test="packageId != null">
                package_id = #{packageId,jdbcType=BIGINT},
            </if>
            <if test="entrancePath != null">
                entrance_path = #{entrancePath,jdbcType=VARCHAR},
            </if>
            <if test="entranceMethod != null">
                entrance_method = #{entranceMethod,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoStrategyPo">
        update algo_strategy
        set note            = #{note,jdbcType=VARCHAR},
            package_id      = #{packageId,jdbcType=BIGINT},
            entrance_path   = #{entrancePath,jdbcType=VARCHAR},
            entrance_method = #{entranceMethod,jdbcType=VARCHAR},
            status          = #{status,jdbcType=INTEGER},
            add_time        = #{addTime,jdbcType=TIMESTAMP},
            update_time     = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <insert id="batchInsert" parameterType="map">
        insert into algo_strategy
        (id, note, package_id, entrance_path, entrance_method, status, add_time,
        update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.note,jdbcType=VARCHAR}, #{item.packageId,jdbcType=BIGINT},
            #{item.entrancePath,jdbcType=VARCHAR}, #{item.entranceMethod,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
</mapper>