<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meishi.stgy.algoplatform.predictor.dal.mapper.PredictionDataScriptMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.PredictionDataScriptPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="owner_mis" jdbcType="VARCHAR" property="ownerMis" />
    <result column="script" jdbcType="VARCHAR" property="script" />
    <result column="script_type" jdbcType="VARCHAR" property="scriptType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_code, note, owner_mis, script, script_type, status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.PredictionDataScriptExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from prediction_data_script
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from prediction_data_script
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from prediction_data_script
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.PredictionDataScriptExample">
    delete from prediction_data_script
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.PredictionDataScriptPo">
    insert into prediction_data_script (id, biz_code, note, 
      owner_mis, script, script_type, 
      status, add_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{bizCode,jdbcType=VARCHAR}, #{note,jdbcType=VARCHAR}, 
      #{ownerMis,jdbcType=VARCHAR}, #{script,jdbcType=VARCHAR}, #{scriptType,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.PredictionDataScriptPo">
    insert into prediction_data_script
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="ownerMis != null">
        owner_mis,
      </if>
      <if test="script != null">
        script,
      </if>
      <if test="scriptType != null">
        script_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="ownerMis != null">
        #{ownerMis,jdbcType=VARCHAR},
      </if>
      <if test="script != null">
        #{script,jdbcType=VARCHAR},
      </if>
      <if test="scriptType != null">
        #{scriptType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.example.PredictionDataScriptExample" resultType="java.lang.Long">
    select count(*) from prediction_data_script
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update prediction_data_script
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.note != null">
        note = #{record.note,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerMis != null">
        owner_mis = #{record.ownerMis,jdbcType=VARCHAR},
      </if>
      <if test="record.script != null">
        script = #{record.script,jdbcType=VARCHAR},
      </if>
      <if test="record.scriptType != null">
        script_type = #{record.scriptType,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update prediction_data_script
    set id = #{record.id,jdbcType=BIGINT},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      note = #{record.note,jdbcType=VARCHAR},
      owner_mis = #{record.ownerMis,jdbcType=VARCHAR},
      script = #{record.script,jdbcType=VARCHAR},
      script_type = #{record.scriptType,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.PredictionDataScriptPo">
    update prediction_data_script
    <set>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="ownerMis != null">
        owner_mis = #{ownerMis,jdbcType=VARCHAR},
      </if>
      <if test="script != null">
        script = #{script,jdbcType=VARCHAR},
      </if>
      <if test="scriptType != null">
        script_type = #{scriptType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.PredictionDataScriptPo">
    update prediction_data_script
    set biz_code = #{bizCode,jdbcType=VARCHAR},
      note = #{note,jdbcType=VARCHAR},
      owner_mis = #{ownerMis,jdbcType=VARCHAR},
      script = #{script,jdbcType=VARCHAR},
      script_type = #{scriptType,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into prediction_data_script
    (id, biz_code, note, owner_mis, script, script_type, status, add_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.bizCode,jdbcType=VARCHAR}, #{item.note,jdbcType=VARCHAR}, 
        #{item.ownerMis,jdbcType=VARCHAR}, #{item.script,jdbcType=VARCHAR}, #{item.scriptType,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>