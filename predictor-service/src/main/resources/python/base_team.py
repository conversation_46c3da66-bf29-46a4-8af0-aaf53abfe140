import abc
import asyncio
import json
import traceback
import uuid
from autogen_agentchat.base import TaskResult
from autogen_agentchat.agents import UserProxyAgent
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.messages import ToolCallExecutionEvent, ToolCallRequestEvent, MemoryQueryEvent
from autogen_agentchat.teams import Selector<PERSON>roup<PERSON>hat, BaseGroupChat
from autogen_core import CancellationToken
from collections import deque
from datetime import datetime
from typing import Dict, Any, Optional, Tuple, Callable

from py4j_env import GatewayManager


class TeamProvider(abc.ABC):
    """
    AutoGen团队管理的抽象基类。
    该类定义了团队的创建、获取、对话执行等核心接口，所有团队管理实现都应继承并实现这些方法。
    """
    def __init__(self):
        self.team: Optional[BaseGroupChat] = None
        self.session_id: Optional[str] = None
        self._message_queue = deque()
        self.cancellation_token: Optional[CancellationToken] = None
        self.sse_connection_pool: Optional['SSEConnectionPool'] = None  # 连接池引用

    @abc.abstractmethod
    async def create_team(
        self
    ) -> Tu<PERSON>[BaseGroupChat, list]:
        """
        获取指定session_id的团队实例，如果不存在则新建。
        同时负责根据session_id加载团队的历史状态（如有）。

        返回：
            team: BaseGroupChat团队对象。
        """
        pass

    def get_test_task(self) -> str:
        return None

    async def run_team_conversation_with_pool(
            self,
            team: SelectorGroupChat,
            initial_user_input: str,
            is_loaded_from_state: bool
        ) -> Any:
            """
            使用连接池模式执行团队对话流程，支持向多个连接广播消息。

            参数：
                team: SelectorGroupChat团队对象。
                initial_user_input: 用户的初始输入。
                is_loaded_from_state: 是否从历史状态恢复。
            返回：
                通常为TaskResult对象，包含对话终止原因、是否成功等信息。
            """
            final_task_result = None
            try:
                run_args = {"cancellation_token": self.cancellation_token}
                if not is_loaded_from_state:
                    run_args["task"] = initial_user_input

                msg_id = None
                async for event in team.run_stream(**run_args):
                    if isinstance(event, TaskResult):
                        final_task_result = event
                    else:
                        if event.type == "ModelClientStreamingChunkEvent":
                            continue
                        output = await self.format_output(event)
                        msg_id = str(uuid.uuid4())
                        if output["messageContent"] is not None and output["messageContent"] != "":
                            await self.broadcast_show_message(msg_id, output, event)
                        if output["currentNodeName"] is not None and output["currentNodeName"] != "":
                            await self.broadcast_node_message(msg_id, output)

                        # 使用广播模式发送原始消息到所有连接
                        await self.broadcast_original_message(event, msg_id)
                        await self.monitor_long_term_task_with_pool(event)
            except Exception as e:
                GatewayManager.get_java_bridge().print(f"Error in run_team_conversation_with_pool for sessionId={self.session_id}: {str(e)}")
                GatewayManager.get_java_bridge().print(traceback.format_exc())

                message_data = {
                    "sessionId": self.session_id,
                    "msgId": str(uuid.uuid4()),
                    "messageType": "错误消息",
                    "source": "system",
                    "content": str(e),
                    "attachment": [traceback.format_exc()],
                    "attachment_type": "错误堆栈",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }

                error_message = {
                    "event": "error_message",
                    "data": {
                        "code": 500,
                        "message": "error",
                        "data": message_data
                    }
                }

                if self.sse_connection_pool:
                    await self.sse_connection_pool.broadcast_to_session(self.session_id, error_message)
                    # 发生错误后关闭所有SSE连接
                    import asyncio
                    asyncio.create_task(self.sse_connection_pool.close_session_connections(self.session_id, delay_seconds=2.0))

            return final_task_result

    async def broadcast_show_message(self, msg_id, output, event):
        """广播显示消息到所有连接"""
        message_data = {
            "sessionId": self.session_id,
            "msgId": msg_id,
            "messageType": "文本消息",
            "source": event.source,
            "content": output['messageContent'],
            "addTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        show_message = {
            "event": "show_message",
            "data": {
                "code": 200,
                "message": "success",
                "data": message_data
            }
        }

        if self.sse_connection_pool:
            sent_count = await self.sse_connection_pool.broadcast_to_session(self.session_id, show_message)
            GatewayManager.get_java_bridge().print(f"Broadcasted show message to {sent_count} connections for sessionId={self.session_id}")

    async def broadcast_original_message(self, message, msg_id):
        """广播原始消息到所有连接"""
        message_data = {
            "msgId": msg_id,
            "sessionId": self.session_id,
            "source": message.source,
            "modelUsage": str(message.models_usage),
            "type": message.type,
            "metaData": str(message.metadata),
            "content": message.to_text()
        }

        original_message = {
            "event": "origin_message",
            "data": {
                "code": 200,
                "message": "success",
                "data": message_data
            }
        }

        if self.sse_connection_pool:
            sent_count = await self.sse_connection_pool.broadcast_to_session(self.session_id, original_message)
            GatewayManager.get_java_bridge().print(f"Broadcasted original message to {sent_count} connections for session {self.session_id}")
        else:
            GatewayManager.get_java_bridge().print(f"No SSE connection pool available for session {self.session_id}")

    async def broadcast_node_message(self, msg_id, output):
        """广播节点消息到所有连接"""
        if output["currentNodeStatus"] != "" and output["currentNodeStatus"] is not None:
            message_data = {
                "msgId": msg_id,
                "sessionId": self.session_id,
                "currentNodeName": output["currentNodeName"],
                "nextNodeName": output["nextNodeName"],
                "currentNodeStatus": output["currentNodeStatus"],
                "nodeLog": output["nodeLog"]
            }

            node_message = {
                "event": "node_message",
                "data": {
                    "code": 200,
                    "message": "success",
                    "data": message_data
                }
            }

            if self.sse_connection_pool:
                sent_count = await self.sse_connection_pool.broadcast_to_session(self.session_id, node_message)
                GatewayManager.get_java_bridge().print(f"Broadcasted node message to {sent_count} connections for sessionId={self.session_id}")

    async def monitor_long_term_task_with_pool(self, message):
        """使用连接池监控长任务"""
        is_long_term_task = "isLongTermTask" in message.to_text()

        if is_long_term_task:
            await self.save_agent_state()
            message_data = {
                "sessionId": self.session_id,
                "msgId": str(uuid.uuid4()),
                "messageType": "交互消息",
                "source": "system",
                "content": "长任务提交完成，执行完成输入 '继续' ，agent将继续执行",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            request_input_message = {
                "event": "pending_message",
                "data": {
                    "code": 200,
                    "message": "success",
                    "data": message_data
                }
            }

            if self.sse_connection_pool:
                sent_count = await self.sse_connection_pool.broadcast_to_session(self.session_id, request_input_message)
                GatewayManager.get_java_bridge().print(f"Broadcasted long task message to {sent_count} connections for sessionId={self.session_id}")

            self.cancellation_token.cancel()

    async def create_sse_input_func(self,
        queue: asyncio.Queue | None # Directly pass the queue from SSEMessageHandler
    ) -> Callable[..., Any]:
        """创建用于UserProxyAgent的自定义input_func，兼容AutoGen调用格式.
           Uses callbacks to interact with SessionManager and UserInputManager to reduce direct coupling here.
        """
        async def sse_input(prompt: str, cancellation_token: Optional[CancellationToken] = None) -> str:
            GatewayManager.get_java_bridge().print("sse_iput has been invoked, prompt: "+prompt)
            if self._message_queue:
                GatewayManager.get_java_bridge().print("sessionId: "+self.session_id+" queue has message!")
                return self._message_queue.popleft()

            message_data = {
                "sessionId": self.session_id,
                "msgId": str(uuid.uuid4()),
                "messageType": "交互消息",
                "source": "system",
                "content": prompt,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            await queue.put({
                "event": "request_input_message",  # 你可以自定义event名
                "data": {
                    "code": 200,
                    "message": "success",
                    "data": message_data
                }
            })



            if self.team:
                await self.save_agent_state()

            if cancellation_token is not None:
                GatewayManager.get_java_bridge().print("cancellation_token is not None.")
                cancellation_token.cancel()
                return "<UserInputCancelled>User input was cancelled.</UserInputCancelled>"
            else:
                GatewayManager.get_java_bridge().print("cancellation_token is None.")

            return ""

        return sse_input

    async def create_sse_input_func_with_pool(self) -> Callable[..., Any]:
        """创建支持连接池的用户输入函数"""
        async def sse_input_with_pool(prompt: str, cancellation_token: Optional[CancellationToken] = None) -> str:
            GatewayManager.get_java_bridge().print("sse_input_with_pool has been invoked, prompt: " + prompt)

            if self._message_queue:
                GatewayManager.get_java_bridge().print("sessionId: " + self.session_id + " queue has message!")
                return self._message_queue.popleft()

            message_data = {
                "sessionId": self.session_id,
                "msgId": str(uuid.uuid4()),
                "messageType": "交互消息",
                "source": "system",
                "content": prompt,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            request_input_message = {
                "event": "request_input_message",
                "data": {
                    "code": 200,
                    "message": "success",
                    "data": message_data
                }
            }

            # 广播输入请求到所有连接
            if self.sse_connection_pool:
                sent_count = await self.sse_connection_pool.broadcast_to_session(self.session_id, request_input_message)
                GatewayManager.get_java_bridge().print(f"Broadcasted input request to {sent_count} connections for sessionId={self.session_id}")

            if self.team:
                await self.save_agent_state()

            if cancellation_token is not None:
                GatewayManager.get_java_bridge().print("cancellation_token is not None.")
                cancellation_token.cancel()
                return "<UserInputCancelled>User input was cancelled.</UserInputCancelled>"
            else:
                GatewayManager.get_java_bridge().print("cancellation_token is None.")

            return ""

        return sse_input_with_pool

    async def format_output(self, message):
        if isinstance(message, TextMessage):
            return message.to_text()
        elif isinstance(message, ToolCallRequestEvent):
            return "请求函数调用中..."
        elif isinstance(message, ToolCallExecutionEvent):
            return "函数调用执行中..."
        elif isinstance(message, MemoryQueryEvent):
            return "记忆读取中..."
        else:
            return message.to_text()

    async def set_user_input_func(self, sse_handler: 'SSEMessageHandler', team_participants: list, team: BaseGroupChat):
        # Bind necessary methods for create_sse_input_func callbacks
        # The UserInputManager and SessionManager instances are passed through from api_server
        user_proxy = None
        for participant in team_participants:
            if type(participant) == UserProxyAgent:
                user_proxy = participant
                break

        if user_proxy is not None:
            # Re-create sse_input_func with the team reference
            final_sse_input_func = await self.create_sse_input_func(
                sse_handler.queue
            )
            user_proxy.input_func = final_sse_input_func

    async def set_user_input_func_with_pool(self, team_participants: list, team: BaseGroupChat):
        """使用连接池模式设置用户输入函数"""
        user_proxy = None
        for participant in team_participants:
            if type(participant) == UserProxyAgent:
                user_proxy = participant
                break

        if user_proxy is not None:
            # 创建支持连接池的输入函数
            final_sse_input_func = await self.create_sse_input_func_with_pool()
            user_proxy.input_func = final_sse_input_func

    def put_message_to_queue(self, message: str):
        """对外暴露的消息写入接口，向消息队列添加一条消息"""
        self._message_queue.append(message)

    def register_test(self):
        return "hello"

    async def save_agent_state(self):
        state_json_data = await self.team.save_state()
        state_json_str = json.dumps(state_json_data, ensure_ascii=False) # save_state returns dict
        pigeon_service_info = {
            "appKey":"com.sankuai.algoplatform.agentapi",
            "interfaceName":"com.sankuai.algoplatform.agentapi.client.service.TAgentStateService",
            "methodName":"saveAgentState"
        }

        state_dto = {
            "sessionId": self.session_id,
            "versionId": str(uuid.uuid4()),
            "teamState": state_json_str
        }
        params_type = ["com.sankuai.algoplatform.agentapi.client.dto.AgentStateDTO"]
        params_value = [json.dumps(state_dto, ensure_ascii=False)]
        type_list = GatewayManager.get_gateway().jvm.java.util.ArrayList()
        for item in params_type:
            type_list.add(item)

        value_list = GatewayManager.get_gateway().jvm.java.util.ArrayList()
        for item in params_value:
            value_list.add(item)

        response = GatewayManager.get_java_bridge().thriftInvoke(json.dumps(pigeon_service_info,ensure_ascii=False), type_list, value_list)
        GatewayManager.get_java_bridge().print(response)

    async def _sse_input_replace(self, prompt, cancellation_token):
        return ""