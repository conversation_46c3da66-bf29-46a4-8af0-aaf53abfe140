# -*- coding: UTF-8 -*-

import threading


class Threadlocal:
    def __init__(self):
        self._local = threading.local()

    def _get_dict(self):
        if not hasattr(self._local, 'data'):
            self._local.data = {}
        return self._local.data

    def set(self, key, value):
        self._get_dict()[key] = value

    def get(self, key, default=None):
        return self._get_dict().get(key, default)

    def delete(self, key):
        self._get_dict().pop(key, None)

    def clear(self):
        self._local.data = {}

    def set_all(self, kvs):
        self._get_dict().update(kvs)

    def get_all(self):
        return self._get_dict()
