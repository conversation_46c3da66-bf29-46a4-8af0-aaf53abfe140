class GatewayManager:
    _instance = None
    _gateway = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(GatewayManager, cls).__new__(cls)
        return cls._instance

    @classmethod
    def set_gateway(cls, gateway):
        cls._gateway = gateway

    @classmethod
    def get_gateway(cls):
        if cls._gateway is None:
            raise RuntimeError("Gateway has not been initialized")
        return cls._gateway

    @classmethod
    def get_java_bridge(cls):
        return cls.get_gateway().jvm.com.sankuai.meishi.stgy.algoplatform.predictor.python.JavaBridge