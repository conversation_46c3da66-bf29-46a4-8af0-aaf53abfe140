# -*- coding: UTF-8 -*-

import sys
import time
import threading
import os
from datetime import datetime
from py4j.java_gateway import JavaGateway, GatewayParameters, CallbackServerParameters

import bridge as bg
import monitor as mon
import py4j_env
import logging
logging.disable(logging.CRITICAL)


def write_startup_log(message):
    """将启动日志写入文件"""
    try:
        # 创建logs目录（如果不存在）
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 生成日志文件名（按日期）
        log_file = os.path.join(log_dir, f"startup_{datetime.now().strftime('%Y%m%d')}.log")

        # 写入日志
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {message}\n")
            f.flush()
    except Exception as e:
        # 如果日志写入失败，仍然输出到控制台
        print(f"Failed to write startup log: {e}")
        print(f"Original message: {message}")


class Py4JServer:
    def __init__(self, gate_port, name, show_name):
        self.port = gate_port
        self.bridge = bg.Bridge()
        self.name = name
        self.show_name = show_name

    def run(self):
        mon.start_report_periodically(self.show_name)
        # 调用Java端超时时间
        gateway = JavaGateway(
            gateway_parameters=GatewayParameters(port=self.port, read_timeout=60 * 10),
            callback_server_parameters=CallbackServerParameters(port=self.port + 1, eager_load=False),
            python_server_entry_point=self.bridge
        )
        py4j_env.GatewayManager.set_gateway(gateway)
        self.bridge.init(gateway)
        gateway.start_callback_server()


if __name__ == "__main__":
    port = 25333
    name = "python_server"
    if len(sys.argv) >= 4:
        name = sys.argv[1]
        port = int(sys.argv[2])
        show_name = sys.argv[3]
        features = sys.argv[4]
    clientServer = Py4JServer(port, name, show_name)
    clientServer.run()
    print("server started.")

    if "agentserver" in features:
        def start_agent_server():
            """在单独线程中启动 FastAPI 服务器"""
            try:
                from agent_server import run_server
                write_startup_log("Starting FastAPI server in background thread...")
                run_server()
            except Exception as e:
                import traceback
                write_startup_log(f"Error starting FastAPI server: {e}")
                write_startup_log(f"FastAPI server error traceback: {traceback.format_exc()}")

        # 在后台线程中启动 FastAPI 服务器
        agent_server_thread = threading.Thread(target=start_agent_server, daemon=True)
        agent_server_thread.start()
        write_startup_log("FastAPI server thread started.")

        # 给 FastAPI 服务器一些时间启动
        time.sleep(2)
        write_startup_log("FastAPI server should be running now.")

    while True:
        time.sleep(60000)
