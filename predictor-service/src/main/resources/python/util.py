# -*- coding: UTF-8 -*-

def flatten(add2list, expand_list):
    for item in expand_list:
        if isinstance(item, list):
            flatten(add2list, item)
        else:
            add2list.append(item)

# def delete_module(modname, paranoid=None):
#     # see: https://stackoverflow.com/questions/1668223/how-to-de-import-a-python-module
#     # https://forum.micropython.org/viewtopic.php?t=5302
#     from sys import modules
#     try:
#         thismod = modules[modname]
#     except KeyError:
#         raise ValueError(modname)
#     these_symbols = dir(thismod)
#     if paranoid:
#         try:
#             paranoid[:]  # sequence support
#         except:
#             raise ValueError('must supply a finite list for paranoid')
#         else:
#             these_symbols = paranoid[:]
#     del modules[modname]
#     for mod in modules.values():
#         try:
#             delattr(mod, modname)
#         except AttributeError:
#             pass
#         if paranoid:
#             for symbol in these_symbols:
#                 if symbol[:2] == '__':  # ignore special symbols
#                     continue
#                 try:
#                     delattr(mod, symbol)
#                 except AttributeError:
#                     pass
#
#
# def len_module():
#     return len(sys.modules)


# if __name__ == "__main__":
#     print("ok")
#     from sys import modules
#     import gc
#     import importlib
#
#     print(len(modules))
#     importlib.import_module('algo_pack.61144ea9517.demo_module.demo')
#     print(len(modules))
#     print(len(gc.get_objects()))
#
#     a = []
#     for k in modules:
#         if k.find('61144ea9517') > -1:
#             a.append(k)
#     for b in a:
#         del modules[b]
#     gc.collect()
#     print(len(gc.get_objects()))
#     print(len(modules))
