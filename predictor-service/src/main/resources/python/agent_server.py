import os
import warnings

# 设置环境变量
os.environ['PYTHONWARNINGS'] = 'ignore'

# 设置警告过滤器
warnings.simplefilter("ignore")
warnings.filterwarnings("ignore", category=RuntimeWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*unawaited coroutine.*")

import asyncio
import time
import json
import traceback
import uuid
from typing import Dict, Any, Optional, List
import uvicorn

from fastapi import FastAPI, Request, BackgroundTasks, HTTPException
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from datetime import datetime
from autogen_core import CancellationToken

from py4j_env import GatewayManager

class TimeoutMiddleware(BaseHTTPMiddleware):
    """请求超时中间件，防止请求无限挂起"""
    def __init__(self, app, timeout: float = 300.0):  # 5分钟超时
        super().__init__(app)
        self.timeout = timeout

    async def dispatch(self, request: Request, call_next):
        try:
            # 对于SSE连接，不设置超时
            if request.url.path.startswith("/api/sse/"):
                return await call_next(request)

            # 其他请求设置超时
            return await asyncio.wait_for(call_next(request), timeout=self.timeout)
        except asyncio.TimeoutError:
            return Response(
                content=json.dumps({"error": "Request timeout", "timeout": self.timeout}),
                status_code=408,
                media_type="application/json"
            )
        except Exception as e:
            return Response(
                content=json.dumps({"error": f"Request error: {str(e)}"}),
                status_code=500,
                media_type="application/json"
            )

class AsyncTaskManager:
    """异步任务管理器，支持超时控制和任务跟踪"""
    def __init__(self):
        self.running_tasks: Dict[str, asyncio.Task] = {}  # session_id -> task
        self.task_lock = asyncio.Lock()
        self.default_timeout = 1200  # 20分钟默认超时
        self.lock_timeout = 5.0  # 锁获取超时时间，防止死锁

    async def _safe_acquire_lock(self, operation_name: str = "unknown") -> bool:
        """安全获取锁，带超时防止死锁"""
        try:
            await asyncio.wait_for(self.task_lock.acquire(), timeout=self.lock_timeout)
            return True
        except asyncio.TimeoutError:
            GatewayManager.get_java_bridge().print(f"Lock acquisition timeout for operation: {operation_name}")
            return False

    def _safe_release_lock(self):
        """安全释放锁"""
        try:
            if self.task_lock.locked():
                self.task_lock.release()
        except Exception as e:
            GatewayManager.get_java_bridge().print(f"Error releasing lock: {e}")

    async def submit_task(self, session_id: str, coro, timeout: Optional[int] = None) -> bool:
        """提交异步任务，支持超时控制"""
        timeout = timeout or self.default_timeout

        # 先获取需要取消的旧任务（如果存在）
        old_task = None
        async with self.task_lock:
            if session_id in self.running_tasks:
                old_task = self.running_tasks[session_id]
                if old_task.done():
                    old_task = None  # 如果已完成，不需要取消

        # 在锁外取消旧任务，避免死锁
        if old_task is not None:
            old_task.cancel()
            GatewayManager.get_java_bridge().print(f"Cancelled existing task for session: {session_id}")
            try:
                await old_task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                GatewayManager.get_java_bridge().print(f"Error while cancelling task for session {session_id}: {e}")

        # 创建新任务并添加到字典
        async with self.task_lock:
            task = asyncio.create_task(self._run_with_timeout(session_id, coro, timeout))
            self.running_tasks[session_id] = task

            GatewayManager.get_java_bridge().print(f"Submitted async task for session: {session_id}, timeout: {timeout}s")
            return True

    async def _run_with_timeout(self, session_id: str, coro, timeout: int):
        """运行带超时的协程"""
        start_time = time.time()
        try:
            GatewayManager.get_java_bridge().print(f"Starting async task for session: {session_id}")

            # 使用 asyncio.wait_for 实现超时控制
            await asyncio.wait_for(coro, timeout=timeout)

            execution_time = time.time() - start_time
            GatewayManager.get_java_bridge().print(f"Async task completed for session: {session_id}, execution time: {execution_time:.2f}s")

        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            GatewayManager.get_java_bridge().print(f"Async task timeout for session: {session_id}, execution time: {execution_time:.2f}s")

            # 发送超时消息给客户端
            await self._send_timeout_message(session_id, execution_time)

        except asyncio.CancelledError:
            execution_time = time.time() - start_time
            GatewayManager.get_java_bridge().print(f"Async task cancelled for session: {session_id}, execution time: {execution_time:.2f}s")
            raise

        except Exception as e:
            execution_time = time.time() - start_time
            GatewayManager.get_java_bridge().print(f"Async task error for session: {session_id}, execution time: {execution_time:.2f}s, error: {e}")
            GatewayManager.get_java_bridge().print(traceback.format_exc())

            # 发送错误消息给客户端
            await self._send_error_message(session_id, str(e))

        finally:
            # 清理任务记录 - 使用非阻塞方式避免死锁
            try:
                async with self.task_lock:
                    if session_id in self.running_tasks:
                        del self.running_tasks[session_id]
            except Exception as cleanup_error:
                # 如果清理失败，记录日志但不影响任务完成
                GatewayManager.get_java_bridge().print(f"Failed to cleanup task record for session {session_id}: {cleanup_error}")

    async def _send_timeout_message(self, session_id: str, execution_time: float):
        """发送超时消息给客户端"""
        try:
            timeout_message_data = {
                "sessionId": session_id,
                "msgId": str(uuid.uuid4()),
                "messageType": "超时消息",
                "source": "system",
                "content": f"任务执行超时，已运行 {execution_time:.1f} 秒，超过最大允许时间 {self.default_timeout} 秒",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "executionTime": execution_time,
                "maxTimeout": self.default_timeout
            }

            timeout_message = {
                "event": "timeout_message",
                "data": {
                    "code": 408,
                    "message": "timeout",
                    "data": timeout_message_data
                }
            }

            # 这里需要引用全局的 connection_manager，稍后会在全局变量部分定义
            if 'connection_manager' in globals():
                await connection_manager.broadcast_to_session(session_id, timeout_message)
                # 超时后关闭连接
                asyncio.create_task(connection_manager.close_session_connections(session_id, delay_seconds=2.0))

        except Exception as e:
            GatewayManager.get_java_bridge().print(f"Error sending timeout message for session {session_id}: {e}")

    async def _send_error_message(self, session_id: str, error_msg: str):
        """发送错误消息给客户端"""
        try:
            error_message_data = {
                "sessionId": session_id,
                "msgId": str(uuid.uuid4()),
                "messageType": "错误消息",
                "source": "system",
                "content": f"任务执行出错: {error_msg}",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }

            error_message = {
                "event": "error_message",
                "data": {
                    "code": 500,
                    "message": "error",
                    "data": error_message_data
                }
            }

            # 这里需要引用全局的 connection_manager
            if 'connection_manager' in globals():
                await connection_manager.broadcast_to_session(session_id, error_message)
                # 错误后关闭连接
                asyncio.create_task(connection_manager.close_session_connections(session_id, delay_seconds=2.0))

        except Exception as e:
            GatewayManager.get_java_bridge().print(f"Error sending error message for session {session_id}: {e}")

    async def cancel_task(self, session_id: str) -> bool:
        """取消指定session的任务"""
        # 先获取要取消的任务
        task_to_cancel = None
        async with self.task_lock:
            if session_id in self.running_tasks:
                task_to_cancel = self.running_tasks[session_id]
                if task_to_cancel.done():
                    # 任务已完成，直接删除记录
                    del self.running_tasks[session_id]
                    return True

        # 如果没有找到任务
        if task_to_cancel is None:
            return False

        # 在锁外取消任务，避免死锁
        task_to_cancel.cancel()
        GatewayManager.get_java_bridge().print(f"Cancelled task for session: {session_id}")

        try:
            await task_to_cancel
        except asyncio.CancelledError:
            pass
        except Exception as e:
            GatewayManager.get_java_bridge().print(f"Error while cancelling task for session {session_id}: {e}")

        # 清理任务记录
        async with self.task_lock:
            if session_id in self.running_tasks:
                del self.running_tasks[session_id]

        return True

    async def get_running_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有运行中的任务信息"""
        async with self.task_lock:
            task_info = {}
            for session_id, task in self.running_tasks.items():
                task_info[session_id] = {
                    "done": task.done(),
                    "cancelled": task.cancelled(),
                    "session_id": session_id
                }
            return task_info

    async def cleanup_finished_tasks(self):
        """清理已完成的任务"""
        async with self.task_lock:
            finished_sessions = []
            for session_id, task in self.running_tasks.items():
                if task.done():
                    finished_sessions.append(session_id)

            for session_id in finished_sessions:
                del self.running_tasks[session_id]
                GatewayManager.get_java_bridge().print(f"Cleaned up finished task for session: {session_id}")

            return len(finished_sessions)



class SSEMessageHandler:
    """处理SSE消息的类"""
    def __init__(self, request: Request, session_id: str = None):
        self.request = request
        self.queue = asyncio.Queue()
        self.is_closed = False
        self.last_activity = time.time()  # 记录最后活动时间（仅真实消息更新）
        self.last_heartbeat = time.time()  # 记录最后心跳时间
        self.disconnect_callback = None  # 断开连接回调
        self.session_id = session_id  # 添加session_id用于日志追踪

    async def stream(self):
        """生成SSE流，支持event字段"""
        try:
            while not self.is_closed:
                try:
                    # 添加超时机制，避免无限等待
                    message = await asyncio.wait_for(self.queue.get(), timeout=90.0)
                except asyncio.TimeoutError:
                    # 超时后发送心跳消息保持连接活跃
                    heartbeat_message = {
                        "event": "heartbeat",
                        "data": {
                            "timestamp": time.time(),
                            "sessionId": self.session_id,
                            "message": "connection_timeout_heartbeat"
                        }
                    }
                    message = heartbeat_message

                if message is None:  # 终止信号
                    break

                # 检查是否为心跳消息
                is_heartbeat = isinstance(message, dict) and message.get("event") == "heartbeat"

                if is_heartbeat:
                    # 心跳消息只更新心跳时间，不更新活动时间
                    self.last_heartbeat = time.time()
                else:
                    # 真实消息更新最后活动时间
                    self.last_activity = time.time()
                    # 同时也更新心跳时间，因为真实消息也表示连接活跃
                    self.last_heartbeat = time.time()

                # 支持event字段
                sse_str = ""
                if isinstance(message, dict):
                    event = message.get("event")
                    data = message.get("data") if "data" in message else message
                    if event:
                        sse_str += f"event: {event}\n"
                    sse_str += f"data: {json.dumps(data)}\n\n"
                else:
                    sse_str = f"data: {json.dumps(message)}\n\n"

                yield sse_str

        except (asyncio.CancelledError, ConnectionResetError, BrokenPipeError) as e:
            session_info = f"sessionId={self.session_id}" if self.session_id else "sessionId=unknown"
            GatewayManager.get_java_bridge().print(f"SSE connection closed: {type(e).__name__}, {session_info}")
            self.is_closed = True
        except Exception as e:
            session_info = f"sessionId={self.session_id}" if self.session_id else "sessionId=unknown"
            GatewayManager.get_java_bridge().print(f"Unexpected error in SSE stream: {e}, {session_info}")
            self.is_closed = True
        finally:
            # 确保连接状态被标记为关闭
            self.is_closed = True
            # 断开连接回调将在wrapped_stream的finally块中调用，避免重复调用

    async def send_message(self, message: Dict[str, Any]):
        """向SSE流发送消息"""
        if not self.is_closed:
            try:
                await self.queue.put(message)
                return True
            except Exception as e:
                session_info = f"sessionId={self.session_id}" if self.session_id else "sessionId=unknown"
                GatewayManager.get_java_bridge().print(f"Failed to send message: {e}, {session_info}")
                self.is_closed = True
                return False
        return False

    async def close(self):
        """关闭SSE流"""
        self.is_closed = True
        await self.queue.put(None)

    def set_disconnect_callback(self, callback):
        """设置断开连接时的回调函数"""
        self.disconnect_callback = callback

    def is_alive(self) -> bool:
        """检查连接是否还活着（仅基于实际消息活动时间）"""
        if self.is_closed:
            return False

        current_time = time.time()
        time_since_activity = current_time - self.last_activity

        # 连接被认为活着的条件：
        # 只检查20分钟内有真实活动，不再检查心跳
        return time_since_activity < 1200  # 20分钟内有真实活动

    async def send_heartbeat(self):
        """发送心跳消息"""
        if not self.is_closed:
            heartbeat_message = {
                "event": "heartbeat",
                "data": {
                    "timestamp": time.time(),
                    "sessionId": self.session_id,
                    "message": "heartbeat"
                }
            }
            try:
                await self.queue.put(heartbeat_message)
                return True
            except Exception as e:
                session_info = f"sessionId={self.session_id}" if self.session_id else "sessionId=unknown"
                GatewayManager.get_java_bridge().print(f"Failed to send heartbeat: {e}, {session_info}")
                return False
        return False


class ConnectionManager:
    """统一的连接和会话管理器，融合了SessionManager和SSEConnectionPool的功能"""
    def __init__(self):
        # 会话数据：session_id -> {team, state_json, sse_handlers}
        self.sessions: Dict[str, Dict[str, Any]] = {}
        # SSE连接池：sessionId -> {misId: SSEMessageHandler}
        self.session_connections: Dict[str, Dict[str, SSEMessageHandler]] = {}
        self.connection_lock = asyncio.Lock()

    def create_session(self, session_id: str):
        """创建新会话或重置现有会话"""
        self.sessions[session_id] = {"team": None, "state_json": None, "sse_handlers": {}}
        return session_id

    def get_session(self, session_id: str):
        """获取会话数据，不存在则创建"""
        if session_id not in self.sessions:
            self.create_session(session_id)
        return self.sessions[session_id]
    async def add_connection(self, session_id: str, mis_id: str, sse_handler: SSEMessageHandler) -> bool:
        """添加SSE连接到连接池"""
        async with self.connection_lock:
            # 确保session存在
            if session_id not in self.sessions:
                self.create_session(session_id)

            # 记录连接池变化前的状态
            old_connection_count = len(self.session_connections.get(session_id, {}))

            if session_id not in self.session_connections:
                self.session_connections[session_id] = {}
                GatewayManager.get_java_bridge().print(f"[连接池] 创建新session: {session_id}, 用户: {mis_id}")

            # 检查是否已存在相同的mis_id连接
            if mis_id in self.session_connections[session_id]:
                old_handler = self.session_connections[session_id][mis_id]
                # 如果旧连接仍然活跃，关闭它
                if not old_handler.is_closed and old_handler.is_alive():
                    GatewayManager.get_java_bridge().print(f"[连接池] 关闭已存在连接: sessionId={session_id}, misId={mis_id}")
                    await old_handler.close()

            # 添加新连接
            self.session_connections[session_id][mis_id] = sse_handler
            # 同时更新session中的sse_handlers
            self.sessions[session_id]["sse_handlers"][mis_id] = sse_handler
            new_connection_count = len(self.session_connections[session_id])

            GatewayManager.get_java_bridge().print(f"[连接池] 添加连接: sessionId={session_id}, misId={mis_id}, 连接数: {old_connection_count} -> {new_connection_count}")
            return True

    async def _remove_connection_unsafe(self, session_id: str, mis_id: str) -> bool:
        """内部方法：在已获取锁的情况下移除连接"""
        if session_id in self.session_connections:
            if mis_id in self.session_connections[session_id]:
                old_connection_count = len(self.session_connections[session_id])
                del self.session_connections[session_id][mis_id]
                new_connection_count = len(self.session_connections[session_id])

                # 同时从session中移除
                if session_id in self.sessions and "sse_handlers" in self.sessions[session_id]:
                    if mis_id in self.sessions[session_id]["sse_handlers"]:
                        del self.sessions[session_id]["sse_handlers"][mis_id]

                GatewayManager.get_java_bridge().print(f"[连接池] 移除连接: sessionId={session_id}, misId={mis_id}, 连接数: {old_connection_count} -> {new_connection_count}")

                # 如果该session没有任何连接了，删除session记录
                if not self.session_connections[session_id]:
                    del self.session_connections[session_id]
                    GatewayManager.get_java_bridge().print(f"[连接池] 删除空session: sessionId={session_id}")
                return True
        return False

    async def remove_connection(self, session_id: str, mis_id: str) -> bool:
        """从连接池移除SSE连接"""
        async with self.connection_lock:
            return self._remove_connection_unsafe(session_id, mis_id)

    async def has_session(self, session_id: str) -> bool:
        """检查sessionId是否存在活跃连接（线程安全版本）"""
        async with self.connection_lock:
            return session_id in self.session_connections and len(self.session_connections[session_id]) > 0

    async def get_session_connections(self, session_id: str) -> Dict[str, SSEMessageHandler]:
        """获取指定session的所有连接（返回副本以避免并发修改）"""
        async with self.connection_lock:
            connections = self.session_connections.get(session_id, {})
            return dict(connections)  # 返回副本

    async def broadcast_to_session(self, session_id: str, message: Dict[str, Any]) -> int:
        """向指定session的所有连接广播消息，返回成功发送的连接数"""
        # 获取连接副本，避免在发送过程中连接池被修改
        connections = await self.get_session_connections(session_id)
        success_count = 0
        failed_connections = []

        for mis_id, sse_handler in connections.items():
            try:
                # 检查连接是否还活着
                if sse_handler.is_closed or not sse_handler.is_alive():
                    failed_connections.append(mis_id)
                    continue

                # 尝试发送消息
                success = await sse_handler.send_message(message)
                if success:
                    success_count += 1
                else:
                    failed_connections.append(mis_id)

            except Exception as e:
                GatewayManager.get_java_bridge().print(f"Exception when sending message to {session_id}:{mis_id}: {e}")
                failed_connections.append(mis_id)

        # 批量清理失败的连接
        if failed_connections:
            await self._batch_remove_connections(session_id, failed_connections)

        return success_count

    async def _batch_remove_connections(self, session_id: str, mis_ids: List[str]):
        """批量移除连接，减少锁获取次数"""
        async with self.connection_lock:
            removed_count = 0
            for mis_id in mis_ids:
                if self._remove_connection_unsafe(session_id, mis_id):
                    removed_count += 1

            if removed_count > 0:
                GatewayManager.get_java_bridge().print(f"[连接池] 批量移除死连接: sessionId={session_id}, 移除数量={removed_count}")

    async def cleanup_dead_connections(self):
        """清理所有死连接"""
        cleaned_count = 0
        async with self.connection_lock:
            sessions_to_remove = []
            for session_id, connections in list(self.session_connections.items()):
                dead_mis_ids = []

                for mis_id, sse_handler in list(connections.items()):
                    if sse_handler.is_closed or not sse_handler.is_alive():
                        dead_mis_ids.append(mis_id)
                        cleaned_count += 1

                # 移除死连接
                for mis_id in dead_mis_ids:
                    del connections[mis_id]
                    # 同时从session中移除
                    if session_id in self.sessions and "sse_handlers" in self.sessions[session_id]:
                        if mis_id in self.sessions[session_id]["sse_handlers"]:
                            del self.sessions[session_id]["sse_handlers"][mis_id]

                # 如果session没有连接了，标记为待删除
                if not connections:
                    sessions_to_remove.append(session_id)

            # 删除空的session
            for session_id in sessions_to_remove:
                del self.session_connections[session_id]

        if cleaned_count > 0:
            GatewayManager.get_java_bridge().print(f"[连接池] 清理死连接: {cleaned_count}个")

        return cleaned_count

    async def close_session_connections(self, session_id: str, delay_seconds: float = 2.0) -> int:
        """关闭指定session的所有连接"""
        connections = await self.get_session_connections(session_id)
        if not connections:
            return 0

        GatewayManager.get_java_bridge().print(f"[连接池] 关闭session连接: sessionId={session_id}, 连接数: {len(connections)}")

        # 延迟关闭，给客户端时间接收最后的消息
        await asyncio.sleep(delay_seconds)

        closed_count = 0
        for mis_id, sse_handler in connections.items():
            try:
                if not sse_handler.is_closed:
                    await sse_handler.close()
                    closed_count += 1
            except Exception as e:
                GatewayManager.get_java_bridge().print(f"[连接池] 关闭连接失败: sessionId={session_id}, misId={mis_id}, 错误: {e}")

        # 清理连接池中的记录
        await asyncio.sleep(0.5)
        remaining_connections = await self.get_session_connections(session_id)
        if remaining_connections:
            async with self.connection_lock:
                for mis_id in list(remaining_connections.keys()):
                    self._remove_connection_unsafe(session_id, mis_id)

        return closed_count

    async def get_connection_count(self, session_id: str) -> int:
        """获取指定session的连接数"""
        connections = await self.get_session_connections(session_id)
        return len(connections)

    async def get_all_sessions(self) -> List[str]:
        """获取所有活跃的sessionId列表"""
        async with self.connection_lock:
            return list(self.session_connections.keys())

    async def send_heartbeat_to_session(self, session_id: str) -> int:
        """向指定session的所有连接发送心跳消息，返回成功发送的连接数"""
        connections = await self.get_session_connections(session_id)
        success_count = 0
        failed_connections = []

        for mis_id, sse_handler in connections.items():
            try:
                if sse_handler.is_closed:
                    failed_connections.append(mis_id)
                    continue

                # 发送心跳消息
                success = await sse_handler.send_heartbeat()
                if success:
                    success_count += 1
                else:
                    failed_connections.append(mis_id)

            except Exception as e:
                GatewayManager.get_java_bridge().print(f"心跳发送异常 {session_id}:{mis_id}: {e}")
                failed_connections.append(mis_id)

        # 批量清理心跳发送失败的连接
        if failed_connections:
            await self._batch_remove_connections(session_id, failed_connections)

        return success_count




class UserInputManager:
    """管理用户输入的异步等待和继续"""
    def __init__(self):
        self.waiting_inputs = {}  # session_id -> Future

    async def wait_for_input(self, session_id: str):
        """等待用户为指定会话提供输入"""
        future = asyncio.Future()
        self.waiting_inputs[session_id] = future
        return await future

    async def provide_input(self, session_id: str, user_input: str):
        """提供用户输入并解除等待"""
        if session_id in self.waiting_inputs:
            future = self.waiting_inputs.pop(session_id)
            future.set_result(user_input)
            return True
        return False


# 创建应用实例
app = FastAPI(title="AutoGen SSE API Server")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加超时中间件
app.add_middleware(TimeoutMiddleware, timeout=300.0)  # 5分钟超时

# 全局变量
connection_manager = ConnectionManager()
input_manager = UserInputManager()
async_task_manager = AsyncTaskManager()

# 连接超时配置（秒）
ACTIVITY_TIMEOUT = 1200  # 20分钟没有真实活动 -> 关闭
HEARTBEAT_INTERVAL = 30  # 30秒心跳间隔

# 后台任务：连接管理
async def connection_management_task():
    """后台任务：清理死连接和强制关闭长时间无响应的连接"""
    while True:
        try:
            await asyncio.sleep(60)  # 每60秒检查一次
            current_time = time.time()

            # 强制关闭长时间无响应的连接
            all_sessions = await connection_manager.get_all_sessions()
            force_closed_count = 0

            for session_id in all_sessions:
                connections = await connection_manager.get_session_connections(session_id)
                for mis_id, sse_handler in connections.items():
                    time_since_activity = current_time - sse_handler.last_activity

                    # 超过20分钟没有真实消息活动 -> 强制关闭
                    if time_since_activity > ACTIVITY_TIMEOUT and not sse_handler.is_closed:
                        try:
                            await sse_handler.close()
                            force_closed_count += 1
                            GatewayManager.get_java_bridge().print(
                                f"强制关闭连接: sessionId={session_id}, misId={mis_id}, 无活动时间={time_since_activity:.1f}s"
                            )
                        except Exception as e:
                            GatewayManager.get_java_bridge().print(f"关闭连接失败 {session_id}:{mis_id}: {e}")

            # 清理已经标记为死亡的连接
            cleaned_count = await connection_manager.cleanup_dead_connections()

            if force_closed_count > 0 or cleaned_count > 0:
                GatewayManager.get_java_bridge().print(f"连接管理: 强制关闭={force_closed_count}, 清理死连接={cleaned_count}")

        except Exception as e:
            GatewayManager.get_java_bridge().print(f"连接管理任务错误: {e}")

# 后台任务：定期发送心跳
async def heartbeat_task():
    """后台任务：定期向所有连接发送心跳消息"""
    while True:
        try:
            await asyncio.sleep(HEARTBEAT_INTERVAL)  # 每30秒发送一次心跳
            all_sessions = await connection_manager.get_all_sessions()
            total_sent = 0

            for session_id in all_sessions:
                sent_count = await connection_manager.send_heartbeat_to_session(session_id)
                total_sent += sent_count

            if total_sent > 0:
                GatewayManager.get_java_bridge().print(f"心跳发送: {total_sent}个连接")
        except Exception as e:
            GatewayManager.get_java_bridge().print(f"心跳任务错误: {e}")

# 后台任务：定期清理已完成的异步任务
async def async_task_cleanup_task():
    """后台任务：定期清理已完成的异步任务"""
    while True:
        try:
            await asyncio.sleep(120)  # 每2分钟清理一次
            cleaned_count = await async_task_manager.cleanup_finished_tasks()
            if cleaned_count > 0:
                GatewayManager.get_java_bridge().print(f"Cleaned up {cleaned_count} finished async tasks")
        except Exception as e:
            GatewayManager.get_java_bridge().print(f"Error in async task cleanup: {e}, sessionId=system_async_task_cleanup")

# 全局变量存储后台任务引用
background_tasks = []

# 启动后台任务
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    try:
        # 创建后台任务并保存引用，避免垃圾回收
        task1 = asyncio.create_task(connection_management_task())
        task2 = asyncio.create_task(heartbeat_task())
        task3 = asyncio.create_task(async_task_cleanup_task())

        background_tasks.extend([task1, task2, task3])

        print("Background tasks started successfully")
    except Exception as e:
        print(f"Error starting background tasks: {e}")

# 优雅关闭处理
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    try:
        # 首先取消所有后台任务
        for task in background_tasks:
            if not task.done():
                task.cancel()

        # 等待后台任务完成取消
        if background_tasks:
            try:
                await asyncio.wait(background_tasks, timeout=5.0, return_when=asyncio.ALL_COMPLETED)
            except asyncio.TimeoutError:
                GatewayManager.get_java_bridge().print("Background tasks cancellation timeout")

        # 取消所有运行中的异步任务
        running_tasks = await async_task_manager.get_running_tasks()
        for session_id in running_tasks.keys():
            await async_task_manager.cancel_task(session_id)

        if running_tasks:
            GatewayManager.get_java_bridge().print(f"Application shutdown: cancelled {len(running_tasks)} running async tasks")

        # 关闭所有活跃的SSE连接
        all_sessions = await connection_manager.get_all_sessions()
        for session_id in all_sessions:
            await connection_manager.close_session_connections(session_id, delay_seconds=0.5)

        GatewayManager.get_java_bridge().print("Application shutdown: all SSE connections closed")
    except Exception as e:
        GatewayManager.get_java_bridge().print(f"Error during shutdown: {e}")

def get_history_state(session_id:str):
    try:
        pigeon_service_info = {
            "appKey": "com.sankuai.algoplatform.agentapi",
            "interfaceName": "com.sankuai.algoplatform.agentapi.client.service.TAgentStateService",
            "methodName": "getAgentState"
        }

        params_type = ["java.lang.String"]
        params_value = [session_id]
        type_list = GatewayManager.get_gateway().jvm.java.util.ArrayList()
        for item in params_type:
            type_list.add(item)

        value_list = GatewayManager.get_gateway().jvm.java.util.ArrayList()
        for item in params_value:
            value_list.add(json.dumps(item,ensure_ascii=False))

        response = GatewayManager.get_java_bridge().thriftInvoke(json.dumps(pigeon_service_info,ensure_ascii=False), type_list, value_list)
        if response is None:
            return None
        result = json.loads(response)
        if result is not None and result['code'] == 200 and result['data'] is not None:
            return result['data']['teamState']
        return None
    except Exception as e:
        GatewayManager.get_java_bridge().print(f"Error getting history state for sessionId={session_id}: {e}")
        GatewayManager.get_java_bridge().print(traceback.format_exc())
        return None

async def process_chat(user_input: str, session_id: str, session: Dict[str, Any], module_name: str, mis_id: str):
    """处理聊天逻辑 - 重构为使用TeamProvider和连接池"""
    try:
        import importlib

        module = importlib.import_module(module_name)
        importlib.reload(module)

        # 根据agentInfo获取team_provider类
        team_provider = module.RealTeamProvider()
        team_provider.session_id = session_id
        team_provider.mis_id = mis_id

        # 设置连接池引用，让TeamProvider可以广播消息
        team_provider.sse_connection_pool = connection_manager

        # 1. 创建team
        team, team_participants = await team_provider.create_team()

        task = team_provider.get_test_task()

        team_provider.team = team
        session["team"] = team

        # 2. 设置user_proxy_agent的input函数 - 使用连接池模式
        await team_provider.set_user_input_func_with_pool(team_participants, team)

        # 3. 准备cancellation token
        team_provider.cancellation_token = CancellationToken()

        # 4. 获取session会话，并加载team
        session_state_json_str = get_history_state(session_id)

        is_loaded_from_state = False
        if session_state_json_str:
            GatewayManager.get_java_bridge().print("get history state!")
            is_loaded_from_state = True
            session_state_data = json.loads(session_state_json_str) if isinstance(session_state_json_str, str) else session_state_json_str
            await team.load_state(session_state_data)

        # 5. 将用户输入添加到team的消息队列
        if user_input and is_loaded_from_state and user_input != "继续":
            GatewayManager.get_java_bridge().print("sessionId:"+session_id+"   add_user_input:" + user_input)
            team_provider.put_message_to_queue(user_input)
        elif not is_loaded_from_state and task is not None:
            GatewayManager.get_java_bridge().print("sessionId:"+session_id+"    get_user_input_from_task:" + task)
            user_input = task

        # 6. Run conversation using TeamProvider - 使用连接池模式
        task_result = await team_provider.run_team_conversation_with_pool(
            team=team,
            initial_user_input=user_input,
            is_loaded_from_state=is_loaded_from_state
        )

        # 7. Handle conversation end - 广播给所有连接
        stop_reason = "unknown"
        is_finished = False
        if task_result is not None:
            stop_reason = task_result.stop_reason or "completed"
            is_finished = True
        else:
            is_finished = False

        if is_finished:
            message_data = {
                "sessionId": session_id,
                "msgId": str(uuid.uuid4()),
                "messageType": "文本消息",
                "source": "system",
                "content": f"对话已结束，原因：{stop_reason}",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }

            # 广播结束消息给所有连接
            finish_message = {
                "event": "finish_message",
                "data": {
                    "code": 200,
                    "message": "success",
                    "data": message_data
                }
            }

            sent_count = await connection_manager.broadcast_to_session(session_id, finish_message)
            GatewayManager.get_java_bridge().print(f"Broadcasted finish message to {sent_count} connections for session {session_id}")

            await team_provider.save_agent_state()

            # 对话结束后关闭所有SSE连接
            asyncio.create_task(connection_manager.close_session_connections(session_id, delay_seconds=2.0))

    except Exception as e:
        GatewayManager.get_java_bridge().print(f"Error in process_chat for sessionId={session_id}: {str(e)}")
        GatewayManager.get_java_bridge().print(traceback.format_exc())

        # 发送错误消息给所有连接
        error_message_data = {
            "sessionId": session_id,
            "msgId": str(uuid.uuid4()),
            "messageType": "错误消息",
            "source": "system",
            "content": f"处理聊天时发生错误: {str(e)}",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        error_message = {
            "event": "error_message",
            "data": {
                "code": 500,
                "message": "error",
                "data": error_message_data
            }
        }

        await connection_manager.broadcast_to_session(session_id, error_message)

        # 发生错误后关闭所有SSE连接
        asyncio.create_task(connection_manager.close_session_connections(session_id, delay_seconds=2.0))


async def process_parser_agent(content: str, session_id: str, module_name):
    """处理解析器代理的逻辑"""
    try:
        # 导入parser_agent_main模块
        # 导入指定模块
        import importlib
        module = importlib.import_module(module_name)
        importlib.reload(module)
        parser_agent_results = module.parser_agent_results

        # 初始化结果
        results = {
            "content": content,  # 用户输入作为初始数据
        }
        # 发送初始消息
        results = parser_agent_results(results)
        msg_id = str(uuid.uuid4())
        await send_parser_agent_message(connection_manager, session_id, results, msg_id)
        await send_node_message(connection_manager, session_id, results, msg_id)
        await request_input_message(connection_manager, session_id, msg_id)

        # 循环处理直到节点信息为end停止
        if results["nodeInfo"]["nextNodeName"] == "flow_end":
            # 发送结束消息
            message_data = {
                "sessionId": session_id,
                "msgId": str(uuid.uuid4()),
                "messageType": "文本消息",
                "source": "system",
                "content": content,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            finish_message = {
                "event": "finish_message",
                "data": {
                    "code": 200,
                    "message": "success",
                    "data": message_data
                }
            }
            await connection_manager.broadcast_to_session(session_id, finish_message)
            asyncio.create_task(connection_manager.close_session_connections(session_id, delay_seconds=2.0))
    except Exception as e:
        GatewayManager.get_java_bridge().print(f"Error in process_parser_agent for sessionId={session_id}: {str(e)}")
        GatewayManager.get_java_bridge().print(traceback.format_exc())

        error_message_data = {
            "sessionId": session_id,
            "msgId": str(uuid.uuid4()),
            "messageType": "错误消息",
            "source": "system",
            "content": content,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        error_message = {
            "event": "error_message",
            "data": {
                "code": 500,
                "message": "error",
                "data": error_message_data
            }
        }
        await connection_manager.broadcast_to_session(session_id, error_message)
async def send_parser_agent_message(connection_manager: ConnectionManager, session_id: str, results: Dict[str, Any], msg_id: str):
    """发送parser的消息"""
    message_data = {
        "sessionId": session_id,
        "msgId": msg_id,
        "messageType": "文本消息",
        "source": "system",
        "content": results["content"],
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    message = {
        "event": "show_message",
        "data": {
            "code": 200,
            "message": "success",
            "data": message_data
        }
    }
    await connection_manager.broadcast_to_session(session_id,message)


async def send_node_message(connection_manager: ConnectionManager, session_id: str, results: Dict[str, Any], msg_id:str):
    message_data = {
        "sessionId": session_id,
        "msgId": msg_id,
        "currentNodeName": results["nodeInfo"]["currentNodeName"],
        "nodeLog": results["nodeInfo"]["node_log"],
        "nextNodeName": results["nodeInfo"]["nextNodeName"]
    }
    message = {
        "event": "node_message",
        "data": {
            "code": 200,
            "message": "success",
            "data": message_data
        }
    }
    await connection_manager.broadcast_to_session(session_id,message)

async def request_input_message(connection_manager: ConnectionManager, session_id: str, msg_id: str):
    message_data = {
        "sessionId": session_id,
        "msgId": msg_id,
        "messageType": "交互消息",
        "source": "system",
        "content": "该节点运行完成，请求下一步输入",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    message = {
        "event": "request_input_message",
        "data": {
            "code": 200,
            "message": "success",
            "data": message_data
        }
    }
    await connection_manager.broadcast_to_session(session_id,message)

@app.get("/api/sse")
async def sse(request: Request, session_id: Optional[str] = None, mis_id: Optional[str] = None):
    """SSE连接端点 - 支持连接池模式"""

    # 验证必需参数
    if not session_id:
        return {
            "error": "session_id参数是必需的",
            "status": "error_missing_session_id"
        }

    if not mis_id:
        return {
            "error": "mis_id参数是必需的，用于标识连接用户",
            "status": "error_missing_mis_id"
        }

    # 检查sessionId是否已存在（如果是第一个连接则创建session）
    has_session = await connection_manager.has_session(session_id)
    if not has_session:
        # 第一个连接到此sessionId，创建session
        connection_manager.create_session(session_id)
        GatewayManager.get_java_bridge().print(f"[连接池] 首次连接创建session: sessionId={session_id}, misId={mis_id}")
    else:
        current_count = await connection_manager.get_connection_count(session_id)
        GatewayManager.get_java_bridge().print(f"[连接池] 连接到已存在session: sessionId={session_id}, misId={mis_id}, 当前连接数: {current_count}")

    # 创建SSE处理器
    sse_handler = SSEMessageHandler(request, session_id)

    # 设置断开连接的回调函数
    async def cleanup_on_disconnect():
        # 获取断开前的连接数
        old_connection_count = await connection_manager.get_connection_count(session_id)

        await connection_manager.remove_connection(session_id, mis_id)

        # 获取断开后的连接数
        new_connection_count = await connection_manager.get_connection_count(session_id)

        GatewayManager.get_java_bridge().print(f"[连接池] 连接断开: sessionId={session_id}, misId={mis_id}, 连接数: {old_connection_count} -> {new_connection_count}")

    sse_handler.set_disconnect_callback(cleanup_on_disconnect)

    # 添加到连接池
    await connection_manager.add_connection(session_id, mis_id, sse_handler)

    # 发送连接成功消息
    connection_count = await connection_manager.get_connection_count(session_id)
    message_data = {
        "sessionId": session_id,
        "misId": mis_id,
        "msgId": str(uuid.uuid4()),
        "messageType": "系统消息",
        "source": "system",
        "content": f"SSE连接已建立，当前session连接数: {connection_count}",
        "addTime": time.strftime("%Y-%m-%d %H:%M:%S")
    }

    await sse_handler.send_message({
        "event": "start_message",
        "data": {
            "code": 200,
            "message": "success",
            "data": message_data
        }
    })

    # 包装stream方法以支持清理
    original_stream = sse_handler.stream()

    async def wrapped_stream():
        cleanup_called = False
        try:
            async for chunk in original_stream:
                yield chunk
        except (asyncio.CancelledError, ConnectionResetError, BrokenPipeError) as e:
            # 连接异常断开，记录日志
            GatewayManager.get_java_bridge().print(f"SSE stream connection error: {type(e).__name__}, sessionId={session_id}, misId={mis_id}")
        except Exception as e:
            # 其他异常
            GatewayManager.get_java_bridge().print(f"SSE stream unexpected error: {e}, sessionId={session_id}, misId={mis_id}")
        finally:
            # 确保连接被清理（双重保险）
            if not cleanup_called:
                try:
                    cleanup_called = True
                    await cleanup_on_disconnect()
                except Exception as cleanup_error:
                    GatewayManager.get_java_bridge().print(f"Error in cleanup_on_disconnect: {cleanup_error}, sessionId={session_id}, misId={mis_id}")

    return StreamingResponse(
        wrapped_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # 禁用nginx缓冲
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )


@app.post("/api/chat")
async def chat(request: Request):
    """聊天API端点 - 使用异步任务管理器替代BackgroundTasks"""
    try:
        data = await request.json()
        session_id = data.get("sessionId")
        user_input = data.get("content")
        module_name = data.get("moduleName")
        agent_code = data.get("agent_code")
        mis_id = data.get("mis_id")
        timeout = data.get("timeout", 1200)  # 允许客户端指定超时时间，默认20分钟

        if not session_id:
            return {
                "error": "session_id参数是必需的",
                "status": "error_missing_session_id"
            }

        # 检查是否有活跃的SSE连接
        has_session = await connection_manager.has_session(session_id)
        if not has_session:
            return {
                "error": f"此会话({session_id})没有活跃的SSE连接。请先建立SSE连接到 /api/sse?session_id={session_id}&mis_id=<your_user_id>",
                "session_id": session_id,
                "status": "error_no_sse_connection"
            }

        # 获取session信息
        session = connection_manager.get_session(session_id)
        connection_count = await connection_manager.get_connection_count(session_id)

        GatewayManager.get_java_bridge().print(f"Processing chat for session: {session_id}, active connections: {connection_count}, timeout: {timeout}s")

        # 使用异步任务管理器提交任务
        if agent_code == 'parser_agent':
            # 创建parser_agent协程
            coro = process_parser_agent(user_input, session_id, module_name)
            await async_task_manager.submit_task(session_id, coro, timeout)
        else:
            # 创建process_chat协程
            coro = process_chat(user_input, session_id, session, module_name, mis_id)
            await async_task_manager.submit_task(session_id, coro, timeout)

        return {
            "session_id": session_id,
            "status": "conversation_processing_started",
            "message": f"聊天请求已接收，正在异步处理。当前连接数: {connection_count}，超时时间: {timeout}秒",
            "active_connections": connection_count,
            "timeout": timeout
        }

    except Exception as e:
        return {
            "error": str(e),
            "traceback": traceback.format_exc(),
            "status": "error"
        }


@app.get("/api/connections")
async def get_connections_info(session_id: Optional[str] = None):
    """获取SSE连接信息"""
    try:
        if session_id:
            # 获取指定session的连接信息
            has_session = await connection_manager.has_session(session_id)
            if not has_session:
                return {
                    "error": f"Session {session_id} not found",
                    "status": "session_not_found"
                }

            connections = await connection_manager.get_session_connections(session_id)
            connection_details = []

            for mis_id, sse_handler in connections.items():
                current_time = time.time()
                connection_details.append({
                    "misId": mis_id,
                    "isAlive": sse_handler.is_alive(),
                    "isClosed": sse_handler.is_closed,
                    "lastActivity": sse_handler.last_activity,
                    "timeSinceLastActivity": current_time - sse_handler.last_activity
                })

            return {
                "sessionId": session_id,
                "connectionCount": len(connections),
                "connections": connection_details,
                "timestamp": time.time()
            }
        else:
            # 获取所有session的连接统计
            all_sessions = await connection_manager.get_all_sessions()
            session_stats = []
            total_connections = 0

            for sid in all_sessions:
                connection_count = await connection_manager.get_connection_count(sid)
                connections = await connection_manager.get_session_connections(sid)

                alive_count = sum(1 for handler in connections.values() if handler.is_alive())

                session_stats.append({
                    "sessionId": sid,
                    "totalConnections": connection_count,
                    "aliveConnections": alive_count
                })
                total_connections += connection_count

            return {
                "totalSessions": len(all_sessions),
                "totalConnections": total_connections,
                "sessions": session_stats,
                "timestamp": time.time()
            }

    except Exception as e:
        return {
            "error": str(e),
            "status": "error"
        }

@app.post("/api/connections/check-and-refresh")
async def check_and_refresh_session(request: Request):
    """检查指定session是否存在SSE连接，如果存在则刷新活动时间"""
    try:
        data = await request.json()
        session_id = data.get("sessionId")
        auto_refresh = data.get("autoRefresh", True)  # 默认自动刷新

        if not session_id:
            return {
                "error": "sessionId参数是必需的",
                "status": "error_missing_session_id"
            }

        # 检查session是否存在
        exists = await connection_manager.has_session(session_id)

        if exists:
            connections = await connection_manager.get_session_connections(session_id)
            connection_count = len(connections)
            alive_count = sum(1 for handler in connections.values() if handler.is_alive())

            # 如果启用自动刷新且有活跃连接，则刷新活动时间
            refreshed_count = 0
            if auto_refresh and alive_count > 0:
                current_time = time.time()
                for mis_id, sse_handler in connections.items():
                    if not sse_handler.is_closed:
                        sse_handler.last_activity = current_time
                        sse_handler.last_heartbeat = current_time
                        refreshed_count += 1

            # 获取连接详情（在刷新后获取，显示最新状态）
            connection_details = []
            for mis_id, sse_handler in connections.items():
                current_time = time.time()
                connection_details.append({
                    "misId": mis_id,
                    "isAlive": sse_handler.is_alive(),
                    "isClosed": sse_handler.is_closed,
                    "lastActivity": sse_handler.last_activity,
                    "timeSinceLastActivity": current_time - sse_handler.last_activity
                })

            return {
                "sessionId": session_id,
                "exists": True,
                "connectionCount": connection_count,
                "aliveConnections": alive_count,
                "refreshed": auto_refresh,
                "refreshedCount": refreshed_count,
                "connections": connection_details,
                "timestamp": time.time(),
                "status": "success"
            }
        else:
            return {
                "sessionId": session_id,
                "exists": False,
                "connectionCount": 0,
                "aliveConnections": 0,
                "refreshed": False,
                "refreshedCount": 0,
                "connections": [],
                "timestamp": time.time(),
                "status": "success"
            }

    except Exception as e:
        return {
            "error": str(e),
            "status": "error"
        }





def run_server(host="0.0.0.0", port=8000):
    """启动服务器"""
    uvicorn.run(
        app,
        host=host,
        port=port,
        timeout_keep_alive=30,
        timeout_graceful_shutdown=15,
        limit_concurrency=500,
        limit_max_requests=5000,
        access_log=False
    )


if __name__ == "__main__":
    run_server()
