# -*- coding: UTF-8 -*-

import importlib
import sys
import ujson
import gc

import predict_context as pc


class Bridge(object):
    """
    Java中PythonBridge接口对应的实现类
    """

    def __init__(self):
        self.name = 'PyBridge'
        self.context = pc.Context()

    def init(self, g):
        self.context.init(g)

    def say_hello(self, arg):
        hello_from_java = self.context.get_java_bridge().sayHello(arg)
        if ('hello:%s' % arg) != hello_from_java:
            raise ValueError("say_hello error" + hello_from_java)
        return 'hello:' + arg

    def invoke(self, module_name, method_name, args, contextdata):
        module = importlib.import_module(module_name)

        params = {
            'context': self.context
        }
        params.update(ujson.loads(args))
        if contextdata != '':
            self.context.contextdata.set_all(ujson.loads(contextdata))
        v = getattr(module, method_name)(**params)
        self.context.contextdata.clear()
        return ujson.dumps(v, ensure_ascii=False)

    def load(self, file_path, module_name):
        sys.path.insert(0, file_path)
        module = importlib.import_module(module_name)
        importlib.reload(module)
        sys.path.pop(0)
        return 0

    def run_gc(self):
        return str(gc.collect())

    def show_memory(self, obj_id):
        res = "*" * 60 + '\n'
        ext_res = "*" * 60 + '\n'
        objects_list = []
        for obj in gc.get_objects():
            size = sys.getsizeof(obj)
            objects_list.append((obj, size))
        for obj, size in sorted(objects_list, key=lambda x: x[1], reverse=True)[:20]:
            res += f"OBJ: {id(obj)}, TYPE: {type(obj)} SIZE: {size / 1024 / 1024:.2f}MB {str(obj)[:200]}\n"
            if obj_id is not None and id(obj) == obj_id:
                referrers = gc.get_referrers(*obj)
                ext_res += str(referrers)
        res = res + '\n\n' + ext_res
        return res
