#!/usr/bin/env bash
set -e

# ${1%_*} 表示从变量 $1 的末尾开始，删除第一个 _ 及其后面的所有字符，然后返回剩下的字符串。
# 比如，如果 $1 是 python3.7_common，那么 ${1%_*} 就是 python3.7
echo "run.sh cmd: $1 ${1%_*} $2 $3 $4"
virtualenv -p "${1%_*}" "$1"
source "$1"/bin/activate "$1"

echo "$1"/bin/python main.py "$1" "$2" "$3" "$4"
exec "$1"/bin/python main.py "$1" "$2" "$3" "$4"

#if [[ "$1" == *pypy* ]]; then
#  echo "$1"/bin/python -X PYPY_GC_MAX=1024MB -X PYPY_GC_MAJOR_COLLECT=1 main.py "$1" "$2" "$3"
#  exec "$1"/bin/python -X PYPY_GC_MAX=1024MB -X PYPY_GC_MAJOR_COLLECT=1 main.py "$1" "$2" "$3"
#else
#  echo "$1"/bin/python main.py "$1" "$2" "$3"
#  exec "$1"/bin/python main.py "$1" "$2" "$3"
#fi