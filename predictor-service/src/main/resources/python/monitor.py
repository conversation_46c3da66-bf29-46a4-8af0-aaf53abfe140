# -*- coding: UTF-8 -*-
"""
Python进程状态上报
"""
import gc
import os
import psutil
import requests
import schedule
import socket
import threading
import time


def get_host_name():
    return socket.gethostname()


def get_psutil_instance():
    pid = os.getpid()
    return psutil.Process(pid)


def get_cpu_user_time():
    return get_psutil_instance().cpu_times().user


def get_cpu_percent():
    return get_psutil_instance().cpu_percent()


def get_memory_size():
    info = get_psutil_instance().memory_full_info()
    return info.uss / 1024. / 1024.


def get_memory_percent():
    return get_psutil_instance().memory_percent()


def get_thread_num():
    return get_psutil_instance().num_threads()


def get_gc_stats():
    return gc.get_stats()


def report_falcon(name):
    try:
        host_name = get_host_name()
        status = {'python.cpu.usertime': get_cpu_user_time(), 'python.cpu.percent': get_cpu_percent(),
                  'python.mem.size': get_memory_size(), 'python.mem.percent': get_memory_percent(),
                  'python.thread.num': get_thread_num()}

        try:
            gc_stat = get_gc_stats()
            for i in range(len(gc_stat)):
                for k, v in gc_stat[i].items():
                    status['python.gc.t%s.%s' % (i, k)] = v
        except Exception as eg:
            print('get_gc_states err')

        all_message = []
        tp = int(time.time())
        for k, v in status.items():
            message = {
                "metric": k,
                "value": v,
                "endpoint": host_name,
                "timestamp": tp,
                "step": 60,
                "counterType": "GAUGE",
                "tags": "name=%s" % name
            }
            all_message.append(message)

        url = 'http://127.0.0.1:1988/v1/push'
        r = requests.post(url, json=all_message)
    except Exception as e1:
        print(e1)


def start_report_periodically(n):
    schedule.every(60).seconds.do(report_falcon, name=n)

    def do_job():
        while True:
            schedule.run_pending()
            time.sleep(1)

    job_thread = threading.Thread(target=do_job)
    job_thread.start()
    print("monitor started.")
