#!/usr/bin/env bash
set -e

## python3.7_common
echo "python environment initializing."
pwd
echo "-- virtualenv: python3.7_common initializing."
python3.7 -m virtualenv --version
python3.7 -m virtualenv -p python3.7 python3.7_common

source python3.7_common/bin/activate
pip3.7 --version
pip3.7 install -r requirements@python3.7_common.txt -i http://pypi.sankuai.com/simple --trusted-host pypi.sankuai.com
deactivate


## python3.9_common
#echo "-- virtualenv: python3.9_common initializing."
#python3.9 -m virtualenv --version
#python3.9 -m virtualenv -p python3.9 python3.9_common
#
#source python3.9_common/bin/activate
#pip3.9 --version
#pip3.9 install -r requirements@python3.9_common.txt -i http://pypi.sankuai.com/simple --trusted-host pypi.sankuai.com
#deactivate

# python3.11_common
echo "-- virtualenv: python3.11_common initializing."
python3.11 -m virtualenv --version
python3.11 -m virtualenv -p python3.11 python3.11_common

source python3.11_common/bin/activate
pip3.11 --version
export PYMUPDF_SETUP_DUMMY=1
pip3.11 install -r requirements@python3.11_common.txt -i http://pypi.sankuai.com/simple --trusted-host pypi.sankuai.com
deactivate

#echo "-- virtualenv: pypy3.8_common initializing."
#virtualenv -p pypy3.8 pypy3.8_common
#
#source pypy3.8_common/bin/activate
#pypy3 -m pip --version
#pypy3 -m pip install -r requirements@pypy3.8_common.txt -i http://pypi.sankuai.com/simple --trusted-host pypi.sankuai.com


echo "-- virtualenv: init done."