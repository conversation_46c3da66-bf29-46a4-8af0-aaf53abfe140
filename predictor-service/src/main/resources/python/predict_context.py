# -*- coding: UTF-8 -*-

import msgpack
import ujson
from py4j.java_gateway import java_import

import util

import threadlocal


class Context(object):
    """
    算法包中使用的预测上下文
    """

    def __init__(self):
        self.gateway = None
        self.view = None
        self.contextdata = threadlocal.Threadlocal()

    def __cache(self):
        if self.view is not None:
            return
        self.view = self.gateway.new_jvm_view()
        java_import(self.view, 'com.sankuai.meishi.stgy.algoplatform.predictor.python.JavaBridge')

    def init(self, g):
        self.gateway = g

    def get_jvm(self):
        return self.gateway.jvm

    def get_java_bridge(self):
        self.__cache()
        return self.view.JavaBridge

    def predict(self, model_name, input, type='int'):
        self.__cache()
        input_json = ujson.dumps(input, ensure_ascii=False)
        output_json = self.view.JavaBridge.predictTFModel(model_name, input_json, type)
        return ujson.loads(output_json)

    def tf_predict(self, model_name, input, type='int'):
        return self.predict(model_name, input, type)

    def predict_with_cache(self, input):
        self.__cache()
        tmpInput = {'contextData': self.contextdata.get_all()}
        tmpInput.update(input)
        input_json = ujson.dumps(tmpInput, ensure_ascii=False)
        output_json = self.view.JavaBridge.predictWithCache(input_json)
        return ujson.loads(output_json)

    def xgb_predict(self, model_name, input, default_value=None):
        self.__cache()
        input_json = ujson.dumps(input, ensure_ascii=False)
        output_json = self.view.JavaBridge.predictXGBModel(model_name, input_json, default_value,
                                                           ujson.dumps(self.contextdata.get_all(), ensure_ascii=False))
        return ujson.loads(output_json)

    def xgb_predict_matrix(self, model_name, input):
        self.__cache()
        batch_size = len(input)
        flatten_input_list = [element for sublist in input for element in sublist]
        input_bytes = msgpack.packb(flatten_input_list)
        value_bytes = self.view.JavaBridge.predictXGBByMatrix(model_name, input_bytes, batch_size,
                                                              ujson.dumps(self.contextdata.get_all(), ensure_ascii=False))
        flatten_value_list = msgpack.unpackb(value_bytes)

        second_dime_len = len(flatten_value_list) // batch_size
        two_dim_value = [flatten_value_list[i * second_dime_len:(i + 1) * second_dime_len] for i in range(batch_size)]
        return two_dim_value

    def triton_predict(self, model_name, model_version, input_value, input_shape, input_type, output_type):
        self.__cache()
        __input_value__ = {}
        for key, shape in input_value.items():
            value_list = []
            value = input_value[key]
            util.flatten(value_list, value)
            __input_value__[key] = value_list

        output_json = self.view.JavaBridge.tritonPredict(model_name, model_version,
                                                         ujson.dumps(__input_value__, ensure_ascii=False),
                                                         ujson.dumps(input_shape, ensure_ascii=False),
                                                         ujson.dumps(input_type, ensure_ascii=False),
                                                         ujson.dumps(output_type, ensure_ascii=False))
        return ujson.loads(output_json)

    def llm_predict(self, biz_code, prompts, extra={"debug_result":"false"}):
        self.__cache()
        prompts_json = ujson.dumps(prompts, ensure_ascii=False)
        extra_json = ujson.dumps(extra, ensure_ascii=False)
        output_json = self.view.JavaBridge.llmPredict(biz_code, prompts_json, extra_json)
        return ujson.loads(output_json)

    def invoke_thrift(self, app_key, service_name, method_name, params_json):
        self.__cache()

        output_json = self.view.JavaBridge.invokeThrift(app_key, service_name, method_name, params_json)
        return ujson.loads(output_json)

    def query_feature(self, group_id, input):
        self.__cache()
        input_json = ujson.dumps(input, ensure_ascii=False)
        output_json = self.view.JavaBridge.queryFeature(group_id, input_json)
        return ujson.loads(output_json)

    def read_lion(self, key, default_value):
        self.__cache()
        return self.view.JavaBridge.readLion(key, default_value)

    def read_tair(self, keys):
        self.__cache()
        keys_json = ujson.dumps(keys, ensure_ascii=False)
        output_json = self.view.JavaBridge.readTair(keys_json)
        return ujson.loads(output_json)

    def cat_num(self, bizCode, num):
        self.__cache()
        self.view.JavaBridge.catNum(bizCode, num)

    def cat_result_num(self, bizCode, reqNum, resNum):
        self.__cache()
        self.view.JavaBridge.catResultNum(bizCode, reqNum, resNum)

    def log_event(self, bizCode, name):
        self.__cache()
        self.view.JavaBridge.logEvent(bizCode, name)

    def log_error(self, bizCode, name, dict):
        self.__cache()
        self.view.JavaBridge.logEventError(bizCode, name, ujson.dumps(dict, ensure_ascii=False))

    def log_metric(self, name, tags, count):
        self.__cache()
        if tags is not None:
            tags = ujson.dumps(tags, ensure_ascii=False)
        self.view.JavaBridge.logMetric(name, tags, count)

    def print(self, str):
        self.__cache()
        self.view.JavaBridge.print(str)
