def abtest(uuid) {
    // 默认策略
    def defaultStgy = 'stgy_v1';
    def stgy = strategyService.getStrategyByLayerId('ab_group_drinkBoard_dealRecReason', uuid, 19534);
    return stgy in ['default', null] ? defaultStgy : stgy;
}

def queryTair(dealIds, stgy) {
    // 对照策略返回空
    if (stgy == 'stgy_blank') {
        return [:];
    }
    def r = [:];
    tairClient.batchGetWithCache('drinkBoard_dealRecReason_', dealIds, '_' + stgy)
            .each { r[it.key as String] = jackson.readValue(it.value, Map.class) };
    return r;
}

def queryBoardRecReason(context) {
    def data = [:];
    // ab实验
    def stgy = abtest(context.reqExtra['uuid']);
    // 查询Tair
    def tair = queryTair(context.entityIds, stgy);
    // 存入结果
    tair.each { data[it.key as String] = [['values': it.value]] }
    return [
            'data' : data,
            'extra': [
                    'stgy': stgy
            ]
    ]
}

queryBoardRecReason(context);