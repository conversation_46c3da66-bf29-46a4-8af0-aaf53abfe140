package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.cache.PredictCacheConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.dto.PredictModelDto;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.impl.SimplePredictServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class PredictPrivateMethodTest {

    @Before
    public void setUp() {

    }

    @Test
    public void testGetPredictCacheConfigWithTestFlag() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        PredictAppServiceImpl service = new PredictAppServiceImpl();

        Method method = PredictAppServiceImpl.class.getDeclaredMethod("getPredictCacheConfig", String.class, Map.class);
        method.setAccessible(true);

        String bizCode = "testBizCode";
        Map<String, String> reqExtra = new HashMap<>();
        reqExtra.put("isTest", "1");
        PredictCacheConfig result = (PredictCacheConfig) method.invoke(service, bizCode, reqExtra);
        assert result == null;
    }

    @Test
    public void testSummaryCacheResult() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        SimplePredictServiceImpl service = new SimplePredictServiceImpl();

        Map<String, String> coupleCache = new HashMap<>();
        Map<String, Object> modelPredictCache = new HashMap<>();
        PredictModelDto predictModelDto = new PredictModelDto();

        Method method = SimplePredictServiceImpl.class.getDeclaredMethod("summaryCacheResult", Map.class, Map.class, PredictModelDto.class);
        method.setAccessible(true);

        List<Map<String, Object>> result = (List<Map<String, Object>>) method.invoke(service, coupleCache, modelPredictCache, predictModelDto);
        assert result.size() == 0;
    }

    @Test
    public void testSummaryNoCacheResult() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        SimplePredictServiceImpl service = new SimplePredictServiceImpl();

        List<List<List<String>>> predictModelList = new ArrayList<>();
        predictModelList.add(new ArrayList<>());
        Map<String, List<String>> noCacheMap = new HashMap<>();
        PredictModelDto predictModelDto = new PredictModelDto();

        Method method = SimplePredictServiceImpl.class.getDeclaredMethod("summaryNoCacheResult", List.class, Map.class, PredictModelDto.class);
        method.setAccessible(true);

        List<Map<String, Object>> result = (List<Map<String, Object>>) method.invoke(service, predictModelList, noCacheMap, predictModelDto);
        assert result.size() == 0;
    }

}
