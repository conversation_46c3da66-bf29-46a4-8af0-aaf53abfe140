package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import static com.sankuai.meishi.stgy.algoplatform.predictor.util.CompressUtil.compress;
import static com.sankuai.meishi.stgy.algoplatform.predictor.util.CompressUtil.decompress;
import static org.junit.Assert.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.zip.Deflater;
import java.util.zip.DeflaterOutputStream;
import java.util.zip.GZIPOutputStream;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

public class CompressUtilTest {

    @Test
    public void compressTest() {
        String origin = "美团以“零售 + 科技”的战略践行“帮大家吃得更好，生活更好”的公司使命。 自2010年3月成立以来，美团持续推动服务零售和商品零售在需求侧和供给侧的数字化升级，和广大合作伙伴一起努力为消费者提供品质服务。2018年9月20日，美团在港交所挂牌上市。 美团始终以客户为中心，不断加大在新技术上的研发投入。美团会和大家一起努力，更好承担社会责任，更多创造社会价值。  \"The quick brown fox jumps over the lazy dog\" is an English-language pangram – a sentence that contains all the letters of the alphabet. The phrase is commonly used for touch-typing practice, testing typewriters and computer keyboards, displaying examples of fonts, and other applications involving text where the use of all letters in the alphabet is desired. The earliest known appearance of the phrase was in The Boston Journal. In an article titled \"Current Notes\" in the February 9, 1885, edition, the phrase is mentioned as a good practice sentence for writing students: \"A favorite copy set by writing teachers for their pupils is the following, because it contains every letter of the alphabet: 'A quick brown fox jumps over the lazy dog.'\"[1] Dozens of other newspapers published the phrase over the next few months, all using the version of the sentence starting with \"A\" rather than \"The\".[2] The earliest known use of the phrase starting with \"The\" is from the 1888 book Illustrative Shorthand by Linda Bronson.[3] The modern form (starting with \"The\") became more common even though it is slightly longer than the original (starting with \"A\"). A 1908 edition of the Los Angeles Herald Sunday Magazine records that when the New York Herald was equipping an office with typewriters \"a few years ago\", staff found that the common practice sentence of \"now is the time for all good men to come to the aid of the party\" did not familiarize typists with the entire alphabet, and ran onto two lines in a newspaper column. They write that a staff member named Arthur F. Curtis invented the \"quick brown fox\" pangram to address this.[4] As the use of typewriters grew in the late 19th century, the phrase began appearing in typing lesson books as a practice sentence. Early examples include How to Become Expert in Typewriting: A Complete Instructor Designed Especially for the Remington Typewriter (1890),[6] and Typewriting Instructor and Stenographer's Hand-book (1892). By the turn of the 20th century, the phrase had become widely known. In the January 10, 1903, issue of Pitman's Phonetic Journal, it is referred to as \"the well known memorized typing line embracing all the letters of the alphabet\".[7] Robert Baden-Powell's book Scouting for Boys (1908) uses the phrase as a practice sentence for signaling.[5] The first message sent on the Moscow–Washington hotline on August 30, 1963, was the test phrase \"THE QUICK BROWN FOX JUMPED OVER THE LAZY DOG'S BACK 1234567890\".[8] Later, during testing, the Russian translators sent a message asking their American counterparts, \"What does it mean when your people say 'The quick brown fox jumped over the lazy dog'?\"[9] \"The quick brown fox jumps over the lazy dog\" is an English-language pangram – a sentence that contains all the letters of the alphabet. The phrase is commonly used for touch-typing practice, testing typewriters and computer keyboards, displaying examples of fonts, and other applications involving text where the use of all letters in the alphabet is desired. The earliest known appearance of the phrase was in The Boston Journal. In an article titled \"Current Notes\" in the February 9, 1885, edition, the phrase is mentioned as a good practice sentence for writing students: \"A favorite copy set by writing teachers for their pupils is the following, because it contains every letter of the alphabet: 'A quick brown fox jumps over the lazy dog.'\"[1] Dozens of other newspapers published the phrase over the next few months, all using the version of the sentence starting with \"A\" rather than \"The\".[2] The earliest known use of the phrase starting with \"The\" is from the 1888 book Illustrative Shorthand by Linda Bronson.[3] The modern form (starting with \"The\") became more common even though it is slightly longer than the original (starting with \"A\"). A 1908 edition of the Los Angeles Herald Sunday Magazine records that when the New York Herald was equipping an office with typewriters \"a few years ago\", staff found that the common practice sentence of \"now is the time for all good men to come to the aid of the party\" did not familiarize typists with the entire alphabet, and ran onto two lines in a newspaper column. They write that a staff member named Arthur F. Curtis invented the \"quick brown fox\" pangram to address this.[4] As the use of typewriters grew in the late 19th century, the phrase began appearing in typing lesson books as a practice sentence. Early examples include How to Become Expert in Typewriting: A Complete Instructor Designed Especially for the Remington Typewriter (1890),[6] and Typewriting Instructor and Stenographer's Hand-book (1892). By the turn of the 20th century, the phrase had become widely known. In the January 10, 1903, issue of Pitman's Phonetic Journal, it is referred to as \"the well known memorized typing line embracing all the letters of the alphabet\".[7] Robert Baden-Powell's book Scouting for Boys (1908) uses the phrase as a practice sentence for signaling.[5] The first message sent on the Moscow–Washington hotline on August 30, 1963, was the test phrase \"THE QUICK BROWN FOX JUMPED OVER THE LAZY DOG'S BACK 1234567890\".[8] Later, during testing, the Russian translators sent a message asking their American counterparts, \"What does it mean when your people say 'The quick brown fox jumped over the lazy dog'?\"[9] ";
        String gzip = compress(origin, "gzip");
        System.out.println(gzip);
        String gzip1 = decompress(gzip, "gzip");
        assert StringUtils.equals(origin, gzip1);
        gzip = compress(origin, "deflate");
        System.out.println(gzip);
        gzip1 = decompress(gzip, "deflate");
        assert StringUtils.equals(origin, gzip1);
        gzip = compress(origin, "deflate_1");
        System.out.println(gzip);
        gzip1 = decompress(gzip, "deflate_1");
        assert StringUtils.equals(origin, gzip1);
    }

    @Test
    public void deTest() {
        String t = "美团以“零售 + 科技”的战略践行“帮大家吃得更好，生活更好”的公司使命。自2010年3月成立以来，美团持续推动服务零售和商品零售在需求侧和供给侧的数字化升级，和广大合作伙伴一起努力为消费者提供品质服务。2018年9月20日，美团在港交所挂牌上市。美团始终以客户为中心，不断加大在新技术上的研发投入。美团会和大家一起努力，更好承担社会责任，更多创造社会价值。";
        String a = compress(t, "gzip");
        String gzip = decompress(a, "gzip");
        assert StringUtils.equals(t, gzip);
    }

    /**
     * 测试 inflate 方法正常解压缩数据
     */
    @Test
    public void testInflateNormalData() throws Throwable {
        // arrange
        String originalString = "This is a test string.";
        byte[] compressedData = compressString(originalString, Deflater.DEFAULT_COMPRESSION);
        // act
        String result = CompressUtil.inflate(compressedData);
        // assert
        assertEquals("解压缩后的字符串应与原始字符串相同", originalString, result);
    }

    /**
     * 测试 inflate 方法传入空数组
     */
    @Test(expected = IOException.class)
    public void testInflateEmptyArray() throws Throwable {
        // arrange
        byte[] emptyData = new byte[0];
        // act
        CompressUtil.inflate(emptyData);
        // assert
        // IOException is expected
    }

    /**
     * 测试 inflate 方法传入非压缩数据
     */
    @Test(expected = IOException.class)
    public void testInflateInvalidData() throws Throwable {
        // arrange
        byte[] invalidData = "Invalid compressed data".getBytes(StandardCharsets.UTF_8);
        // act
        CompressUtil.inflate(invalidData);
        // assert
        // IOException is expected
    }

    // Helper method to compress a string using Deflater
    private static byte[] compressString(String inputString, int compressionLevel) {
        if (inputString == null || inputString.length() == 0) {
            throw new IllegalArgumentException("Cannot compress null or empty string");
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Deflater deflater = new Deflater(compressionLevel);
        DeflaterOutputStream dos = new DeflaterOutputStream(baos, deflater);
        try {
            dos.write(inputString.getBytes(StandardCharsets.UTF_8));
            dos.close();
        } catch (IOException e) {
            // This should never happen with a ByteArrayOutputStream
            throw new AssertionError(e);
        }
        return baos.toByteArray();
    }

    /**
     * 测试 deflate 方法，使用无效的压缩级别（小于0）
     */
    @Test
    public void testDeflateValidInputInvalidLevelBelow() throws Throwable {
        // arrange
        String input = "Test String";
        int level = -1;
        // act
        byte[] result = CompressUtil.deflate(input, level);
        // assert
        assertNotNull("压缩结果不应为 null", result);
        assertTrue("压缩结果应为非空", result.length > 0);
    }

    /**
     * 测试 deflate 方法，输入非空字符串和有效压缩级别
     */
    @Test
    public void testDeflateValidInputValidLevel() throws Throwable {
        // arrange
        String input = "Test String";
        int level = 5;
        // act
        byte[] result = CompressUtil.deflate(input, level);
        // assert
        assertNotNull("压缩结果不应为 null", result);
        assertTrue("压缩结果应为非空", result.length > 0);
    }

    /**
     * 测试 deflate 方法，输入空字符串
     */
    @Test
    public void testDeflateEmptyInputValidLevel() throws Throwable {
        // arrange
        String input = "";
        int level = 5;
        // act
        byte[] result = CompressUtil.deflate(input, level);
        // assert
        assertNotNull("压缩结果不应为 null", result);
        assertTrue("空字符串压缩后的长度应大于等于 0", result.length >= 0);
    }

    /**
     * 测试 deflate 方法，使用最低压缩级别
     */
    @Test
    public void testDeflateValidInputLowestLevel() throws Throwable {
        // arrange
        String input = "Test String";
        int level = 0;
        // act
        byte[] result = CompressUtil.deflate(input, level);
        // assert
        assertNotNull("压缩结果不应为 null", result);
        assertTrue("压缩结果应为非空", result.length > 0);
    }

    /**
     * 测试 deflate 方法，使用最高压缩级别
     */
    @Test
    public void testDeflateValidInputHighestLevel() throws Throwable {
        // arrange
        String input = "Test String";
        int level = 9;
        // act
        byte[] result = CompressUtil.deflate(input, level);
        // assert
        assertNotNull("压缩结果不应为 null", result);
        assertTrue("压缩结果应为非空", result.length > 0);
    }

    /**
     * 测试 deflate 方法，使用无效的压缩级别（大于9）
     */
    @Test(expected = IllegalArgumentException.class)
    public void testDeflateValidInputInvalidLevelAbove() throws Throwable {
        // arrange
        String input = "Test String";
        int level = 10;
        // act
        CompressUtil.deflate(input, level);
        // assert
        // IllegalArgumentException expected
    }

    /**
     * 测试 unGzip 方法输入空字节数组时抛出 IOException
     */
    @Test(expected = IOException.class)
    public void testUnGzipEmptyData() throws Throwable {
        // arrange
        byte[] emptyData = new byte[0];
        // act
        CompressUtil.unGzip(emptyData);
        // assert is handled by the expected exception
    }

    /**
     * 测试 unGzip 方法正常解压 GZIP 数据
     */
    @Test
    public void testUnGzipNormalData() throws Throwable {
        // arrange
        String originalString = "This is the original string to be compressed";
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream);
        gzipOutputStream.write(originalString.getBytes(StandardCharsets.UTF_8));
        gzipOutputStream.close();
        byte[] compressedData = byteArrayOutputStream.toByteArray();
        // act
        String result = CompressUtil.unGzip(compressedData);
        // assert
        Assert.assertEquals("The uncompressed string should match the original string", originalString, result);
    }

    /**
     * 测试 unGzip 方法输入非 GZIP 数据时抛出 IOException
     */
    @Test(expected = IOException.class)
    public void testUnGzipInvalidData() throws Throwable {
        // arrange
        byte[] invalidData = "This is not GZIP data".getBytes(StandardCharsets.UTF_8);
        // act
        CompressUtil.unGzip(invalidData);
        // assert is handled by the expected exception
    }
}
