package com.sankuai.meishi.stgy.algoplatform.predictor.service.util;

import org.tensorflow.framework.DataType;
import org.tensorflow.framework.TensorProto;
import org.tensorflow.framework.TensorShapeProto;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * Tests for {@link TensorUtil#buildFloatTensor(List, int, int)}.
 */
public class TensorUtil_BuildFloatTensorTest {

    /**
     * 测试当维度值为负数时构建张量。
     */
    @Test
    public void testBuildFloatTensor_NegativeDimensions() {
        // arrange
        List<Float> floatList = Arrays.asList(1.0f, 2.0f, 3.0f);
        int dim1 = -1;
        int dim2 = -3;
        // act
        TensorProto tensorProto = TensorUtil.buildFloatTensor(floatList, dim1, dim2);
        // assert
        assertNotNull(tensorProto);
        TensorShapeProto shapeProto = tensorProto.getTensorShape();
        assertEquals(2, shapeProto.getDimCount());
        // 验证实际行为，如果方法没有抛出异常，那么我们应该验证返回的 TensorProto 是否符合预期
        // 例如，我们可以检查维度是否被设置为负数，或者是否有其他行为
        // 以下是一个示例断言，实际断言应根据被测方法的实际行为来编写
        assertEquals(dim1, shapeProto.getDim(0).getSize());
        assertEquals(dim2, shapeProto.getDim(1).getSize());
    }

    // ... 其他测试方法保持不变 ...
    /**
     * 测试正常情况下构建浮点数张量。
     */
    @Test
    public void testBuildFloatTensor_Normal() {
        // arrange
        List<Float> floatList = Arrays.asList(1.0f, 2.0f, 3.0f);
        int dim1 = 1;
        int dim2 = 3;
        // act
        TensorProto tensorProto = TensorUtil.buildFloatTensor(floatList, dim1, dim2);
        // assert
        assertNotNull(tensorProto);
        assertEquals(DataType.DT_FLOAT, tensorProto.getDtype());
        TensorShapeProto shapeProto = tensorProto.getTensorShape();
        assertEquals(2, shapeProto.getDimCount());
        assertEquals(dim1, shapeProto.getDim(0).getSize());
        assertEquals(dim2, shapeProto.getDim(1).getSize());
        assertEquals(floatList, tensorProto.getFloatValList());
    }

    // ... 其他测试方法保持不变 ...
    /**
     * 测试当浮点数列表为空时构建张量。
     */
    @Test
    public void testBuildFloatTensor_EmptyList() {
        // arrange
        List<Float> floatList = Collections.emptyList();
        int dim1 = 1;
        int dim2 = 0;
        // act
        TensorProto tensorProto = TensorUtil.buildFloatTensor(floatList, dim1, dim2);
        // assert
        assertNotNull(tensorProto);
        assertTrue(tensorProto.getFloatValList().isEmpty());
    }

    /**
     * 测试当浮点数列表为null时构建张量。
     */
    @Test(expected = NullPointerException.class)
    public void testBuildFloatTensor_NullList() {
        // arrange
        List<Float> floatList = null;
        int dim1 = 1;
        int dim2 = 3;
        // act
        TensorUtil.buildFloatTensor(floatList, dim1, dim2);
    }

    /**
     * 测试当维度值为零时构建张量。
     */
    @Test
    public void testBuildFloatTensor_ZeroDimensions() {
        // arrange
        List<Float> floatList = Arrays.asList(1.0f, 2.0f, 3.0f);
        int dim1 = 0;
        int dim2 = 0;
        // act
        TensorProto tensorProto = TensorUtil.buildFloatTensor(floatList, dim1, dim2);
        // assert
        assertNotNull(tensorProto);
        TensorShapeProto shapeProto = tensorProto.getTensorShape();
        assertEquals(2, shapeProto.getDimCount());
        assertEquals(dim1, shapeProto.getDim(0).getSize());
        assertEquals(dim2, shapeProto.getDim(1).getSize());
    }
}
