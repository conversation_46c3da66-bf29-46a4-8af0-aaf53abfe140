package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.google.common.collect.ImmutableMap;
import org.junit.Assert;
import org.junit.Test;

public class GroovyScriptFactoryTest {


    @Test
    public void runScriptTest() {
        String script = "def a = 1;\n def c = a + b;";
        Object c = GroovyScriptFactory.runScript(script, ImmutableMap.of("b", 1));
        Assert.assertEquals(c, 2);
    }
//
//    @Before
//    public void setUp() throws NoSuchFieldException, IllegalAccessException {
//        // 使用反射设置innerLruCache为mockCache
//        Field innerLruCacheField = GroovyScriptFactory.class.getDeclaredField("innerLruCache");
//        innerLruCacheField.setAccessible(true);
//        innerLruCacheField.set(null, mockCache);
//        // 初始化scriptClass
//        String script = "println 'Hello, World!'";
//        scriptClass = groovyClassLoader.parseClass(script);
//        assertNotNull("scriptClass should not be null after parsing", scriptClass);
//        // 清理mockCache
//        reset(mockCache);
//    }
//
    /**
     * 测试正常解析脚本并缓存
     */
//    @Test
//    public void testGetScriptClassNormal() throws Throwable {
//        // arrange
//        String script = "println 'Hello, World!'";
//        String scriptKey = DigestUtils.md5Hex(script).toLowerCase();
//        Class<Script> scriptClass = groovyClassLoader.parseClass(script);
//        when(mockCache.get(eq(scriptKey), any())).thenReturn(scriptClass);
//        // act
//        Class<Script> result = GroovyScriptFactory.getScriptClass(script);
//        // assert
//        assertNotNull(result);
//        verify(mockCache).get(eq(scriptKey), any());
//    }
//
    /**
     * 测试脚本内容为空
     */
//    @Test(expected = RuntimeException.class)
//    public void testGetScriptClassEmptyScript() throws Throwable {
//        // arrange
//        String script = "";
//        String scriptKey = DigestUtils.md5Hex(script).toLowerCase();
//        when(mockCache.get(eq(scriptKey), any())).thenThrow(new ExecutionException(new Throwable()));
//        // act
//        GroovyScriptFactory.getScriptClass(script);
//        // assert is handled by the expected exception
//    }
//
    /**
     * 测试解析脚本异常
     */
//    @Test(expected = RuntimeException.class)
//    public void testGetScriptClassParseException() throws Throwable {
//        // arrange
//        String script = "invalid script";
//        String scriptKey = DigestUtils.md5Hex(script).toLowerCase();
//        when(mockCache.get(eq(scriptKey), any())).thenThrow(new ExecutionException(new Throwable()));
//        // act
//        GroovyScriptFactory.getScriptClass(script);
//        // assert is handled by the expected exception
//    }
}
