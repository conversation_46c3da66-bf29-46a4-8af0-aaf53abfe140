package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictionQueryContext;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import com.dianping.lion.client.ConfigRepository;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

@RunWith(MockitoJUnitRunner.class)
public class LogToolsTest {

    @Mock
    private Logger predictContextLogger;

    @Mock
    private Logger modelErrorLogger;

    @Mock
    private ConfigRepository configRepository;

    private static final String PREDICT_CONTEXT_LOG_SWITCH = "predict_context_log_switch";

    @Before
    public void setUp() {
        // 模拟 ConfigRepository 实例
//        when(configRepository.getBooleanValue(PREDICT_CONTEXT_LOG_SWITCH, true)).thenReturn(true);
        // 由于不需要校验日志，这里不再模拟 LoggerFactory
    }

    @Test
    public void testLogContext_WhenLionConfigTrue_ShouldLog() {
        PredictionQueryContext context = new PredictionQueryContext();
        context.setBizCode("testBizCode");
        LogTools.logContext(context);
        // 由于不需要校验日志，这里不进行验证
    }

    @Test
    public void testLogContext_WhenLionConfigFalse_ShouldNotLog() {
//        when(configRepository.getBooleanValue(PREDICT_CONTEXT_LOG_SWITCH, true)).thenReturn(false);
        PredictionQueryContext context = new PredictionQueryContext();
        LogTools.logContext(context);
        // 由于不需要校验日志，这里不进行验证
    }

//    @Test
//    public void testLogContext_WhenExceptionOccurs_ShouldLogError() {
//        PredictionQueryContext context = new PredictionQueryContext();
//        doThrow(new RuntimeException("Logging exception")).when(predictContextLogger).info(anyString());
//        try {
//            LogTools.logContext(context);
//        } catch (Exception ignored) {
//        }
//        // 由于不需要校验日志，这里不进行验证
//    }
}
