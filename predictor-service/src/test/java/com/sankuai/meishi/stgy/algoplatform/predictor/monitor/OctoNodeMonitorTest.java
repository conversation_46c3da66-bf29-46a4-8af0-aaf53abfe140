package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import com.sankuai.meishi.stgy.algoplatform.predictor.crane.MonitorTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;

@RunWith(MockitoJUnitRunner.class)
public class OctoNodeMonitorTest {

    @Mock
    OctoNodeMonitorService service;

    @InjectMocks
    MonitorTask monitorTask;

    @Test
    public void testNodeMonitor() {
        String appKey = "com.sankuai.algoplatform.modelserver";
        service.serverNodeMonitor();
        assert appKey.equals("com.sankuai.algoplatform.modelserver");
    }

    @Test
    public void testMonitorTask() {
        String appKeys = "[\"com.sankuai.algoplatform.predictor\"]";
        doNothing().when(service).serverNodeMonitor();
        monitorTask.serverNodeMonitor();
        assert appKeys.equals("[\"com.sankuai.algoplatform.predictor\"]");
    }
}
