package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.alibaba.fastjson.JSON;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.dto.PredictModelDto;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.Logger;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;
import org.slf4j.LoggerFactory;
import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.InjectMocks.*;

@RunWith(MockitoJUnitRunner.class)
public class LogTools_LogModelLogTest {

    @Mock
    private Logger modelErrorLoggerMock;

    @Before
    public void setUp() throws Exception {
        Field modelErrorLoggerField = LogTools.class.getDeclaredField("modelErrorLogger");
        modelErrorLoggerField.setAccessible(true);
        // 移除 final 修饰符
        Field modifiersField = Field.class.getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(modelErrorLoggerField, modelErrorLoggerField.getModifiers() & ~Modifier.FINAL);
        modelErrorLoggerField.set(null, modelErrorLoggerMock);
    }

    /**
     * 测试 logModelLog 方法，正常情况
     */
    @Test
    public void testLogModelLogNormalCase() {
        // arrange
        PredictModelDto modelDto = new PredictModelDto();
        modelDto.setModel_name("testModel");
        String expectedLog = JSON.toJSONString(modelDto);
        // act
        LogTools.logModelLog(modelDto);
        // assert
        verify(modelErrorLoggerMock).info(contains(expectedLog));
    }

    /**
     * 测试 logModelLog 方法，传入 null 对象
     */
    @Test
    public void testLogModelLogWithNull() {
        // arrange
        PredictModelDto modelDto = null;
        // act
        LogTools.logModelLog(modelDto);
        // assert
        verify(modelErrorLoggerMock, times(1)).info(anyString());
    }

    // 使用反射设置 LogTools 中的静态 Logger 字段
    private void setInternalState(Class<?> clazz, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(null, value);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
