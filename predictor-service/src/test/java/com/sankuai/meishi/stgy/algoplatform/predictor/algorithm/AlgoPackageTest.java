package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm;


import com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.util.LocationUtil;
import org.junit.Test;

import java.io.File;

public class AlgoPackageTest {

    @Test
    public void pullCodeTest() {
        String version = "bc0506e8096";
        AlgoPackage apackage = new AlgoPackage();
        apackage.setVersion(version);
        apackage.setCodeRepo(Constants.ALGO_PACKAGE_GIT_URL);

        apackage.pullCode();

        String basePath = LocationUtil.getRuntimePath() + "/python";
        File f = new File(basePath + "/algo_pack");

        File v1File = new File(f.getAbsolutePath() + "/" + version);
        assert v1File.exists() && v1File.listFiles() != null && v1File.listFiles().length > 0;
    }

    @Test
    public void pullCodeTest1() {
        String notExistVersion = "notExistVersionNotExist";
        AlgoPackage apackage = new AlgoPackage();
        apackage.setVersion(notExistVersion);
        apackage.setCodeRepo(Constants.ALGO_PACKAGE_GIT_URL);

        Exception exception = null;
        try {
            apackage.pullCode();
        } catch (Exception e) {
            exception = e;
        }
        assert exception != null;
        System.out.println(exception.getMessage());


        String basePath = LocationUtil.getRuntimePath() + "/python";
        File f = new File(basePath + "/algo_pack");

        File v1File = new File(f.getAbsolutePath() + "/" + notExistVersion);
        assert !v1File.exists();
    }

    @Test
    public void pullCodeTest2() {
        String version = "d2640b4b7fe";
        AlgoPackage apackage = new AlgoPackage();
        apackage.setCodeRepo("ssh://*******************/dcgstgy/bm-dashboard.git");
        apackage.setVersion(version);

        apackage.pullCode();

        String basePath = LocationUtil.getRuntimePath() + "/python";
        File f = new File(basePath + "/algo_pack");

        File v1File = new File(f.getAbsolutePath() + "/" + version);
        assert v1File.exists() && v1File.listFiles() != null && v1File.listFiles().length > 0;
    }

}
