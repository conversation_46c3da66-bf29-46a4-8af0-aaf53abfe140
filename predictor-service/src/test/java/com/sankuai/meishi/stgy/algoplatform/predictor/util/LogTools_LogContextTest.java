package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictContext;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;

import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ LogTools.class, Lion.class, XMDLogFormat.class })
public class LogTools_LogContextTest {

    @Mock
    private ConfigRepository configRepositoryMock;

    @Mock
    private Logger predictContextLoggerMock;

    @Mock
    private Logger modelErrorLoggerMock;

    @Mock
    private PredictContext predictContextMock;

    @Mock
    private XMDLogFormat xmdLogFormatMock;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(Lion.class);
        when(Lion.getConfigRepository()).thenReturn(configRepositoryMock);
        PowerMockito.mockStatic(LogTools.class);
        PowerMockito.mockStatic(XMDLogFormat.class);
        PowerMockito.when(XMDLogFormat.build()).thenReturn(xmdLogFormatMock);
        PowerMockito.doCallRealMethod().when(LogTools.class);
        LogTools.logContext(any(PredictContext.class));
        try {
            PowerMockito.when(LogTools.class, "getPredictContextLogger").thenReturn(predictContextLoggerMock);
            PowerMockito.when(LogTools.class, "getModelErrorLogger").thenReturn(modelErrorLoggerMock);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testLogContext_WhenLionConfigIsFalse_ShouldNotLog() {
        // arrange
        when(configRepositoryMock.getBooleanValue(anyString(), anyBoolean())).thenReturn(false);
        // act
        LogTools.logContext(predictContextMock);
        // assert
        verify(predictContextLoggerMock, never()).info(anyString());
    }
}
