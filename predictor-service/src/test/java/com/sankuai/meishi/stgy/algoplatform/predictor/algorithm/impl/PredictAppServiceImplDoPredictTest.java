package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.AlgoStrategyDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.BizStrategyDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoStrategyPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BizStrategyPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackage;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoStrategy;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AbtestDistributor;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.cache.PredictCacheConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.cache.PredictCacheContent;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.TairClient;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackageService;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictContext;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith;
import static org.mockito.Mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PredictAppServiceImplDoPredictTest {

    @InjectMocks
    private PredictAppServiceImpl predictAppService;

    @Mock
    private BizStrategyDao bizStrategyDao;

    @Mock
    private AlgoStrategyDao algoStrategyDao;

    @Mock
    private AlgoPackageService algoPackageService;

    @Mock
    private TairClient tairClient;

    @Mock
    private PredictContext context;

    @Mock
    private AlgoPackage algoPackage;

    private Method doPredictMethod;

    @Before
    public void setUp() throws NoSuchMethodException {
        doPredictMethod = PredictAppServiceImpl.class.getDeclaredMethod("doPredict", PredictContext.class);
        doPredictMethod.setAccessible(true);
    }

    private void setupNormalCase() {
        BizStrategyPo bizStrategyPo = new BizStrategyPo();
        bizStrategyPo.setAbtestConfig("{\"strategyName\":\"testStrategy\",\"distributions\":[{\"name\":\"testDistribution\",\"strategyId\":1,\"quota\":100}]}");
        when(bizStrategyDao.getValidByCodeWithCache(anyString())).thenReturn(bizStrategyPo);
        AlgoStrategyPo algoStrategyPo = new AlgoStrategyPo();
        algoStrategyPo.setId(1L);
        algoStrategyPo.setPackageId(1L);
        algoStrategyPo.setEntrancePath("testPath");
        algoStrategyPo.setEntranceMethod("testMethod");
        when(algoStrategyDao.getValidByIdWithCache(anyLong())).thenReturn(algoStrategyPo);
        when(context.getReq()).thenReturn(new HashMap<>());
        when(context.getRespExtra()).thenReturn(new HashMap<>());
    }

    @Test(expected = Exception.class)
    public void testDoPredictException() throws Throwable {
        when(bizStrategyDao.getValidByCodeWithCache(anyString())).thenThrow(new Exception());
        when(context.getBizCode()).thenReturn("validBizCode");
        doPredictMethod.invoke(predictAppService, context);
    }

    @Test(expected = Exception.class)
    public void testDoPredictAbtestNull() throws Throwable {
        when(bizStrategyDao.getValidByCodeWithCache(anyString())).thenReturn(null);
        when(context.getBizCode()).thenReturn("validBizCode");
        doPredictMethod.invoke(predictAppService, context);
    }

    @Test(expected = Exception.class)
    public void testDoPredictDistributionNull() throws Throwable {
        setupNormalCase();
        BizStrategyPo bizStrategyPo = new BizStrategyPo();
        bizStrategyPo.setAbtestConfig("{\"strategyName\":\"testStrategy\",\"distributions\":[]}");
        when(bizStrategyDao.getValidByCodeWithCache(anyString())).thenReturn(bizStrategyPo);
        when(context.getBizCode()).thenReturn("validBizCode");
        doPredictMethod.invoke(predictAppService, context);
    }

    @Test(expected = Exception.class)
    public void testDoPredictStrategyIdNull() throws Throwable {
        setupNormalCase();
        when(algoStrategyDao.getValidByIdWithCache(anyLong())).thenReturn(null);
        when(context.getBizCode()).thenReturn("validBizCode");
        doPredictMethod.invoke(predictAppService, context);
    }
}
