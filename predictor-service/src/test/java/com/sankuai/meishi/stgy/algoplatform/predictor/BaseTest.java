package com.sankuai.meishi.stgy.algoplatform.predictor;

import com.meituan.ut.toolkit.mock.util.CraneMockUtils;
import com.meituan.ut.toolkit.mock.util.OctoMockUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

//@RunWith(SpringRunner.class)
//@SpringBootTest
public class BaseTest {
    static {
//        针对OCTO/Crane的注册逻辑进行Mock
        OctoMockUtils.mockRegister();
        CraneMockUtils.mockRegister();
    }

//    @Test
    public void base() {
    }
}
