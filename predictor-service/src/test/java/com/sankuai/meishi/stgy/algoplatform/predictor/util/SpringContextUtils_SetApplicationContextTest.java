package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import static org.junit.Assert.*;
import java.lang.reflect.Field;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

@RunWith(MockitoJUnitRunner.class)
public class SpringContextUtils_SetApplicationContextTest {

    private SpringContextUtils springContextUtils;

    @Mock
    private ApplicationContext mockApplicationContext;

    @Before
    public void setUp() {
        springContextUtils = new SpringContextUtils();
    }

    /**
     * 测试 setApplicationContext 方法在传入非空 ApplicationContext 时是否正确赋值
     */
    @Test
    public void testSetApplicationContextWithNonNullContext() {
        // arrange (准备阶段不需要任何操作)
        // act
        springContextUtils.setApplicationContext(mockApplicationContext);
        // assert
        assertNotNull("应该成功设置 applicationContext", SpringContextUtils.getApplicationContext());
    }

    /**
     * 测试 setApplicationContext 方法在传入 null 时的行为
     */
    @Test
    public void testSetApplicationContextWithNullContext() {
        // arrange (准备阶段不需要任何操作)
        // act
        springContextUtils.setApplicationContext(null);
        // assert
        assertNull("applicationContext 应该为 null", SpringContextUtils.getApplicationContext());
    }
}
