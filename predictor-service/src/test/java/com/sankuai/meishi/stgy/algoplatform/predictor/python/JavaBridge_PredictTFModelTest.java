package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimplePredictService;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.SpringContextUtils;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.context.ApplicationContext;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks;

@RunWith(MockitoJUnitRunner.class)
public class JavaBridge_PredictTFModelTest {

    @Mock
    private ApplicationContext mockApplicationContext;

    @Mock
    private SimplePredictService mockPredictService;

    @After
    public void tearDown() throws Exception {
        // 通过反射重置 SpringContextUtils 中的 applicationContext 静态字段
        Field applicationContextField = SpringContextUtils.class.getDeclaredField("applicationContext");
        applicationContextField.setAccessible(true);
        applicationContextField.set(null, null);
        Mockito.reset(mockPredictService);
    }

    private void setMockApplicationContext() throws Exception {
        // 通过反射设置 SpringContextUtils 中的 applicationContext 静态字段
        Field applicationContextField = SpringContextUtils.class.getDeclaredField("applicationContext");
        applicationContextField.setAccessible(true);
        applicationContextField.set(null, mockApplicationContext);
    }

    // 测试预测模型，输入类型为 double
    @Test
    public void testPredictTFModelTypeDouble() throws Exception {
        setMockApplicationContext();
        when(mockApplicationContext.getBean(SimplePredictService.class)).thenReturn(mockPredictService);
        Map<String, List<List<Number>>> input = Collections.singletonMap("test", Collections.singletonList(Collections.singletonList(1.0)));
        Map<String, List<?>> expectedOutput = Collections.singletonMap("result", Collections.singletonList(1.0));
        when(mockPredictService.predict(eq("testModel"), eq(Double.class), any())).thenReturn(expectedOutput);
        String result = JavaBridge.predictTFModel("testModel", JSONObject.toJSONString(input), "double");
        Assert.assertEquals(JSONObject.toJSONString(expectedOutput), result);
    }
}
