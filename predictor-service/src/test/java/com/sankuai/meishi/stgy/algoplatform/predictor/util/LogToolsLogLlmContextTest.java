package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictContext;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.dto.PredictModelDto;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ Lion.class, LogTools.class, LoggerFactory.class })
@PowerMockIgnore({ "javax.script.*", "javax.management.*", "com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "org.w3c.*" })
public class LogToolsLogLlmContextTest {

    @Mock
    private PredictContext context;

    @Mock
    private ConfigRepository configRepository;

    @Mock
    private Logger predictContextLogger;

    private static final String LLM_PREDICT_CONTEXT_LOG_SWITCH = "llmPredictContextLogSwitch";

    @Before
    @SuppressWarnings("unchecked")
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        // Mock static methods
        mockStatic(Lion.class);
        mockStatic(LoggerFactory.class);
        // Configure Lion mock
        when(Lion.getConfigRepository()).thenReturn(configRepository);
        // Configure Logger mock
        when(LoggerFactory.getLogger("scribe")).thenReturn(predictContextLogger);
        doNothing().when(predictContextLogger).info(anyString());
        // Configure context mock
        when(context.getBizCode()).thenReturn("testBizCode");
        when(context.getReq()).thenReturn((Map) new HashMap<String, Object>());
        when(context.getReqExtra()).thenReturn(new HashMap<String, String>());
        when(context.getResp()).thenReturn((Map) new HashMap<String, Object>());
        when(context.getRespExtra()).thenReturn(new HashMap<String, String>());
    }

    //    @Test
    //    public void testLogContext_WhenExceptionOccurs_ShouldLogError() {
    @After
    public void tearDown() {
        // Reset all static mocks
        try {
            org.powermock.api.mockito.PowerMockito.doCallRealMethod().when(Lion.class, "getConfigRepository");
            org.powermock.api.mockito.PowerMockito.doCallRealMethod().when(LoggerFactory.class, "getLogger", anyString());
        } catch (Exception e) {
            // Ignore cleanup errors
        }
    }

    //        PredictionQueryContext context = new PredictionQueryContext();
    //        doThrow(new RuntimeException("Logging exception")).when(predictContextLogger).info(anyString());
    //        try {
    //            LogTools.logContext(context);
    //        } catch (Exception ignored) {
    //        }
    //        // 由于不需要校验日志，这里不进行验证
    //    }
    @Test
    public void testLogLlmContextSwitchOff() throws Throwable {
        // Configure switch to be off
        when(configRepository.getBooleanValue(LLM_PREDICT_CONTEXT_LOG_SWITCH, true)).thenReturn(false);
        // Call the method under test
        LogTools.logLlmContext(context);
        // Verify no interactions
        verify(context, never()).getBizCode();
        verify(predictContextLogger, never()).info(anyString());
    }
}
