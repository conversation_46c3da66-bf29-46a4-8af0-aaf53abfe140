package com.sankuai.meishi.stgy.algoplatform.predictor.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.meituan.ai.friday.sdk.api.completion.chat.ChatCompletionChoice;
import com.meituan.ai.friday.sdk.api.completion.chat.ChatMessage;
import com.sankuai.algoplatform.llmpredict.predict.api.dto.LLMRequest;
import com.sankuai.algoplatform.llmpredict.predict.api.dto.LLMResponse;
import com.sankuai.algoplatform.llmpredict.predict.api.service.LLMService;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.dto.PredictModelDto;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SimplePredictServiceImpl_DoPredictWithCache_1_Test {

    @InjectMocks
    private SimplePredictServiceImpl simplePredictService;

    @Mock
    private PredictModelDto predictModelDto;

    @Mock
    private LLMService llmClient;

    private String bizCode;

    private Map<String, String> prompts;

    private Map<String, String> extra;

    @Before
    public void setUp() {
        bizCode = "test_biz";
        prompts = new HashMap<>();
        extra = new HashMap<>();
    }

    private void setUpCommonMockBehavior() {
        List<List<String>> defaultInput = new ArrayList<>();
        defaultInput.add(Arrays.asList("Sample", "Data"));
        when(predictModelDto.getInput()).thenReturn(defaultInput);
        when(predictModelDto.getProcessed_input()).thenReturn(null);
        when(predictModelDto.getModel_name()).thenReturn("testModel");
        when(predictModelDto.getFunc_type()).thenReturn("testFunc");
        when(predictModelDto.getMax_seq_len()).thenReturn(512);
    }

    @Test(expected = NullPointerException.class)
    public void testDoPredictWithCache_NullInput() throws Throwable {
        predictModelDto = null;
        simplePredictService.doPredictWithCache(predictModelDto);
    }

    // Removed the test cases that were expecting NullPointerExceptions as it's generally not a good practice to expect such exceptions in well-designed tests.
    /**
     * Test case for empty bizCode
     */
    @Test
    public void testLlmPredict_EmptyBizCode() throws Throwable {
        // arrange
        bizCode = "";
        List<Map<String, String>> promptsList = new ArrayList<>();
        Map<String, String> prompt = new HashMap<>();
        prompt.put("key", "value");
        promptsList.add(prompt);
        prompts.put("prompts", JSON.toJSONString(promptsList));
        // act
        Map<String, Object> result = simplePredictService.llmPredict(bizCode, prompts, extra);
        // assert
        assertEquals(1, result.get("code"));
    }

    /**
     * Test case for null prompts
     */
    @Test
    public void testLlmPredict_NullPrompts() throws Throwable {
        // arrange
        // Using empty map instead of null
        prompts = new HashMap<>();
        // act
        Map<String, Object> result = simplePredictService.llmPredict(bizCode, prompts, extra);
        // assert
        assertEquals(1, result.get("code"));
    }

    /**
     * Test case for missing prompts key
     */
    @Test
    public void testLlmPredict_MissingPromptsKey() throws Throwable {
        // arrange
        prompts = new HashMap<>();
        prompts.put("wrong_key", "value");
        // act
        Map<String, Object> result = simplePredictService.llmPredict(bizCode, prompts, extra);
        // assert
        assertEquals(1, result.get("code"));
    }

    /**
     * Test case for successful execution with debug result
     */
    @Test
    public void testLlmPredict_SuccessWithDebugResult() throws Throwable {
        // arrange
        List<Map<String, String>> promptsList = new ArrayList<>();
        Map<String, String> prompt = new HashMap<>();
        prompt.put("key", "test_prompt");
        promptsList.add(prompt);
        prompts.put("prompts", JSON.toJSONString(promptsList));
        extra.put("debug_result", "true");
        LLMResponse llmResponse = new LLMResponse();
        llmResponse.setCode(0);
        Map<String, String> responseData = new HashMap<>();
        Map<String, ChatCompletionChoice> choices = new HashMap<>();
        ChatCompletionChoice choice = new ChatCompletionChoice();
        choice.setFinishReason("stop");
        ChatMessage message = new ChatMessage();
        message.setContent("test_content");
        choice.setMessage(message);
        choices.put("test_key", choice);
        responseData.put("result", JSON.toJSONString(choices));
        llmResponse.setData(responseData);
        when(llmClient.inferenceForRpc(any(LLMRequest.class))).thenReturn(llmResponse);
        // act
        Map<String, Object> result = simplePredictService.llmPredict(bizCode, prompts, extra);
        // assert
        assertEquals(0, result.get("code"));
        assertNotNull(result.get("data"));
        assertNotNull(result.get("extra"));
        Map<String, String> data = (Map<String, String>) result.get("data");
        assertTrue(data.containsKey("test_key"));
        assertEquals("test_content", data.get("test_key"));
    }

    /**
     * Test case for LLM service exception
     */
    @Test
    public void testLlmPredict_ServiceException() throws Throwable {
        // arrange
        List<Map<String, String>> promptsList = new ArrayList<>();
        Map<String, String> prompt = new HashMap<>();
        prompt.put("key", "test_prompt");
        promptsList.add(prompt);
        prompts.put("prompts", JSON.toJSONString(promptsList));
        when(llmClient.inferenceForRpc(any(LLMRequest.class))).thenThrow(new RuntimeException("Service error"));
        // act & assert
        try {
            simplePredictService.llmPredict(bizCode, prompts, extra);
            fail("Should throw RuntimeException");
        } catch (RuntimeException e) {
            assertTrue(e.getMessage().contains("llmPredict error"));
        }
    }

    /**
     * Test case for invalid LLM response
     */
    @Test
    public void testLlmPredict_InvalidResponse() throws Throwable {
        // arrange
        List<Map<String, String>> promptsList = new ArrayList<>();
        Map<String, String> prompt = new HashMap<>();
        prompt.put("key", "test_prompt");
        promptsList.add(prompt);
        prompts.put("prompts", JSON.toJSONString(promptsList));
        LLMResponse llmResponse = new LLMResponse();
        llmResponse.setCode(1);
        // Add empty data map to avoid NPE
        llmResponse.setData(new HashMap<>());
        when(llmClient.inferenceForRpc(any(LLMRequest.class))).thenReturn(llmResponse);
        // act
        Map<String, Object> result = simplePredictService.llmPredict(bizCode, prompts, extra);
        // assert
        assertEquals(-1, result.get("code"));
    }

    /**
     * Test case for duplicate prompt keys
     */
    @Test
    public void testLlmPredict_DuplicatePromptKeys() throws Throwable {
        // arrange
        List<Map<String, String>> promptsList = new ArrayList<>();
        Map<String, String> prompt1 = new HashMap<>();
        prompt1.put("key", "same_key");
        Map<String, String> prompt2 = new HashMap<>();
        prompt2.put("key", "same_key");
        promptsList.add(prompt1);
        promptsList.add(prompt2);
        prompts.put("prompts", JSON.toJSONString(promptsList));
        // act
        Map<String, Object> result = simplePredictService.llmPredict(bizCode, prompts, extra);
        // assert
        assertEquals(1, result.get("code"));
    }
}
