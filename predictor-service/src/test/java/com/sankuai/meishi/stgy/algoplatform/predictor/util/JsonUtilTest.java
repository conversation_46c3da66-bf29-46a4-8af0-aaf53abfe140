package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.cache.PredictCacheContent;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024/4/10
 */
public class JsonUtilTest {

    @Test
    public void test() {
        String algorithmVersion = "qwejkj123asd";

        PredictCacheContent cacheContent = new PredictCacheContent();
        cacheContent.setAlgorithmVersion(algorithmVersion);
        cacheContent.setCacheMap(ImmutableMap.of("k1", "v1", "k2", "v2"));

        String json = JsonUtils.toJson(cacheContent);

        Assert.assertNotNull(json);

        JSONObject jsonObject = JsonUtils.toJsonObject(cacheContent);

        Assert.assertNotNull(jsonObject);
        Assert.assertEquals(algorithmVersion, jsonObject.getString("algorithmVersion"));

        PredictCacheContent predictCacheContent = JsonUtils.convertObject(null, PredictCacheContent.class);
        Assert.assertNull(predictCacheContent);

        predictCacheContent = JsonUtils.convertObject(cacheContent, PredictCacheContent.class);
        Assert.assertNotNull(predictCacheContent);

        Assert.assertEquals(algorithmVersion, predictCacheContent.getAlgorithmVersion());
        Assert.assertEquals(2, predictCacheContent.getCacheMap().size());
        Assert.assertEquals("v1", predictCacheContent.getCacheMap().get("k1").toString());

    }
}
