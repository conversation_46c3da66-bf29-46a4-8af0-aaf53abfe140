package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.context.ApplicationContext;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

@RunWith(MockitoJUnitRunner.class)
public class SpringContextUtils_ContainsBeanTest {

    @Mock
    private ApplicationContext mockApplicationContext;

    private SpringContextUtils springContextUtils;

    @Before
    public void setUp() {
        springContextUtils = new SpringContextUtils();
        springContextUtils.setApplicationContext(mockApplicationContext);
    }

    /**
     * 测试 applicationContext 为 null 时 containsBean 方法的行为
     */
    @Test(expected = NullPointerException.class)
    public void testContainsBeanWhenApplicationContextIsNull() {
        // arrange
        springContextUtils.setApplicationContext(null);
        // act
        SpringContextUtils.containsBean("someBean");
        // assert
        // 期望抛出 NullPointerException
    }

    /**
     * 测试 applicationContext 非 null 且包含指定名称的 bean 时 containsBean 方法的行为
     */
    @Test
    public void testContainsBeanWhenBeanIsPresent() {
        // arrange
        String beanName = "existingBean";
        when(mockApplicationContext.containsBean(beanName)).thenReturn(true);
        // act
        boolean result = SpringContextUtils.containsBean(beanName);
        // assert
        assertTrue("应当返回 true，因为 bean 存在", result);
    }

    /**
     * 测试 applicationContext 非 null 且不包含指定名称的 bean 时 containsBean 方法的行为
     */
    @Test
    public void testContainsBeanWhenBeanIsAbsent() {
        // arrange
        String beanName = "nonExistingBean";
        when(mockApplicationContext.containsBean(beanName)).thenReturn(false);
        // act
        boolean result = SpringContextUtils.containsBean(beanName);
        // assert
        assertFalse("应当返回 false，因为 bean 不存在", result);
    }
}
