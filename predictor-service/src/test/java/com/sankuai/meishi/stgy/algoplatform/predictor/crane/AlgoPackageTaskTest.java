package com.sankuai.meishi.stgy.algoplatform.predictor.crane;

import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackageService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

/**
 * AlgoPackageTask单元测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class AlgoPackageTaskTest {

    @Mock
    private AlgoPackageService algoPackageService;

    @InjectMocks
    private AlgoPackageTask algoPackageTask;

    /**
     * 测试refreshPackages方法，当AlgoPackageService返回正常更新数量时
     */
    @Test
    public void testRefreshPackagesNormalBehavior() {
        // arrange
        int expectedRefreshCnt = 5;
        when(algoPackageService.refresh(false)).thenReturn(expectedRefreshCnt);
        // act
        algoPackageTask.refreshPackages();
        // assert
        verify(algoPackageService).refresh(false);
        verifyNoMoreInteractions(algoPackageService);
    }

    /**
     * 测试refreshPackages方法，当AlgoPackageService抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testRefreshPackagesException() {
        // arrange
        when(algoPackageService.refresh(false)).thenThrow(new RuntimeException());
        // act
        algoPackageTask.refreshPackages();
        // assert
        verify(algoPackageService).refresh(false);
        verifyNoMoreInteractions(algoPackageService);
    }

    /**
     * 测试refreshPackages方法，当AlgoPackageService返回零更新数量时
     */
    @Test
    public void testRefreshPackagesNoUpdates() {
        // arrange
        int expectedRefreshCnt = 0;
        when(algoPackageService.refresh(false)).thenReturn(expectedRefreshCnt);
        // act
        algoPackageTask.refreshPackages();
        // assert
        verify(algoPackageService).refresh(false);
        verifyNoMoreInteractions(algoPackageService);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 移除了设置log的代码，因为log是由Slf4j自动生成的
    }
}
