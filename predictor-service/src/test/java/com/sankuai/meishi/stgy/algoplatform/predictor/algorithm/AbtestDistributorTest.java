package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;
import org.mockito.junit.MockitoJUnitRunner;

public class AbtestDistributorTest {

    @Test
    public void getDistributionConfig() {
        String s = "{\"strategyName\":\"s1\",\"distributions\":[{\"name\":\"group_a\",\"strategyId\":1,\"quota\":50},{\"name\":\"group_b\",\"strategyId\":2,\"quota\":50}]}";
        AbtestDistributor instance = AbtestDistributor.getInstance(s);
        AbtestDistributor.DistributionConfig distributionConfig1 = instance.getDistributionConfig("a1");
        AbtestDistributor.DistributionConfig distributionConfig2 = instance.getDistributionConfig("b1");
        Assert.assertEquals(distributionConfig1.getName(), "group_a");
        Assert.assertEquals(distributionConfig2.getName(), "group_b");
    }

    /**
     * 测试 getStrategyName 方法能否正确返回策略名称
     */
    @Test
    public void testGetStrategyNameReturnsCorrectValue() {
        // arrange
        AbtestDistributor distributor = new AbtestDistributor();
        String expectedStrategyName = "testStrategy";
        distributor.setStrategyName(expectedStrategyName);
        // act
        String actualStrategyName = distributor.getStrategyName();
        // assert
        Assert.assertEquals("策略名称应该与设置的值相同", expectedStrategyName, actualStrategyName);
    }

    /**
     * 测试 getStrategyName 方法在策略名称被设置时能否正确返回策略名称
     */
    @Test
    public void testGetStrategyNameWhenSet() {
        // arrange
        AbtestDistributor distributor = new AbtestDistributor();
        String expectedStrategyName = "testStrategy";
        distributor.setStrategyName(expectedStrategyName);
        // act
        String actualStrategyName = distributor.getStrategyName();
        // assert
        Assert.assertEquals("当策略名称被设置时，getStrategyName 应该返回相应的策略名称", expectedStrategyName, actualStrategyName);
    }

    /**
     * 测试 getStrategyName 方法在策略名称未被设置时能否返回 null
     */
    @Test
    public void testGetStrategyNameWhenNotSet() {
        // arrange
        AbtestDistributor distributor = new AbtestDistributor();
        // 不设置 strategyName，默认为 null
        // act
        String actualStrategyName = distributor.getStrategyName();
        // assert
        Assert.assertNull("当策略名称未被设置时，getStrategyName 应该返回 null", actualStrategyName);
    }

    /**
     * 测试正常情况下的 JSON 字符串解析和实例获取
     */
    @Test
    public void testGetInstanceNormal() {
        // arrange
        String jsonStr = "{\"strategyName\":\"testStrategy\",\"distributions\":[{\"name\":\"group_a\",\"strategyId\":1,\"quota\":50},{\"name\":\"group_b\",\"strategyId\":2,\"quota\":50}]}";
        // act
        AbtestDistributor distributor = AbtestDistributor.getInstance(jsonStr);
        // assert
        assertNotNull(distributor);
        assertEquals("testStrategy", distributor.getStrategyName());
        assertEquals(2, distributor.getDistributions().size());
        assertEquals(100, distributor.getDistributions().stream().mapToInt(AbtestDistributor.DistributionConfig::getQuota).sum());
    }

    /**
     * 测试 JSON 字符串为空的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetInstanceJsonStringEmpty() {
        // arrange
        String jsonStr = "{\"strategyName\":\"testStrategy\"}";
        // act
        AbtestDistributor.getInstance(jsonStr);
        // assert is handled by the expected exception
    }

    /**
     * 测试 distributions 列表为空的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetInstanceDistributionsEmpty() {
        // arrange
        String jsonStr = "{\"strategyName\":\"testStrategy\",\"distributions\":[]}";
        // act
        AbtestDistributor.getInstance(jsonStr);
        // assert is handled by the expected exception
    }

    /**
     * 测试 distributions 列表中的 quota 总和不等于 100 的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetInstanceQuotaSumNot100() {
        // arrange
        String jsonStr = "{\"strategyName\":\"testStrategy\",\"distributions\":[{\"name\":\"group_a\",\"strategyId\":1,\"quota\":30},{\"name\":\"group_b\",\"strategyId\":2,\"quota\":60}]}";
        // act
        AbtestDistributor.getInstance(jsonStr);
        // assert is handled by the expected exception
    }
}
