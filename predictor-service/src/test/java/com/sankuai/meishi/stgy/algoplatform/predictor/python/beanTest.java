package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import static com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants.APPKEY;

import com.google.common.collect.ImmutableList;
import com.google.gson.Gson;
import com.meituan.service.mobile.mtthrift.generic.GenericService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.meishi.stgy.algoplatform.application.client.promptmanage.prompt.IPromptService;
import com.sankuai.meishi.stgy.algoplatform.application.client.promptmanage.prompt.TPromptCommonReq;
import com.sankuai.meishi.stgy.algoplatform.application.client.promptmanage.prompt.TPromptDetailResponse;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.SpringContextUtils;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

//@RunWith(SpringRunner.class)
//@SpringBootTest
public class beanTest
{
    public static final ConcurrentHashMap<String, ThriftClientProxy> clientCache =
        new ConcurrentHashMap();

//    @Test
    public void beanTest() throws Exception {
        Object object    = SpringContextUtils.getBean("promptsServiceClient");
        Class  beanClass = object.getClass();
        beanClass.getMethods();
        Method method = beanClass.getDeclaredMethod(
            "getPromptDetailById", new Class[] {TPromptCommonReq.class});
        TPromptCommonReq req = new TPromptCommonReq();
        req.setSceneId(39);
        req.setPromptId(138);
        String json = new Gson().toJson(req);
        Object result =
            method.invoke(object, new Object[] {new Gson().fromJson(json, TPromptCommonReq.class)});
        TPromptDetailResponse response =
            new Gson().fromJson(new Gson().toJson(result), TPromptDetailResponse.class);
        return;
    }

//    @Test
    public void thriftTest() throws Exception {
//        String appKey = "com.sankuai.algoplatform.appserver";
//        String serviceName =
//            "com.sankuai.meishi.stgy.algoplatform.application.client.promptmanage.prompt.IPromptService";
//        String methodName = "getPromptDetailById";
//
//        ThriftClientProxy clientProxy    = getClientProxy(appKey, serviceName);
//        GenericService    genericService = (GenericService)clientProxy.getObject();
//
//        String result = genericService.$invoke(
//            methodName,
//            ImmutableList.of(""),
//            ImmutableList.of("{\"sceneId\":39,\"promptId\":138}"));
    }

}
