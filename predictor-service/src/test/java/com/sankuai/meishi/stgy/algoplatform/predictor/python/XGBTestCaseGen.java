package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.msgpack.core.MessageBufferPacker;
import org.msgpack.core.MessagePack;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class XGBTestCaseGen {

    public static void main(String[] args) throws IOException {
        List<String> featureNames = Arrays.asList("deal_num_sim", "dining_time_sim", "discount_sim", "market_price_sim", "name_jaccard_sim", "name_lcs_sim", "dish_sim", "dining_date_sim", "dj_market_price", "dj_price", "mt_market_price", "mt_price", "dj_deal_num_sub", "mt_deal_num_sub", "max_deal_num_sub", "min_deal_num_sub", "dj_deal_num", "mt_deal_num", "dj_discount", "mt_discount", "market_price_div", "mt_discount_div", "deal_max_num", "deal_min_num", "dj_market_price_sub", "market_price_sub", "dj_price_sub", "mt_price_sub", "dj_dining_time_b", "dj_dining_time_l", "dj_dining_time_s", "dj_dining_time_t", "dj_dining_time_n", "mt_dining_time_b", "mt_dining_time_l", "mt_dining_time_s", "mt_dining_time_t", "mt_dining_time_n", "market_price_origin_diff", "price_origin_diff", "market_price_log_diff", "price_log_diff", "dish_model_sim", "dish_sim_mean", "mt_dish_sim", "dj_dish_sim");
        int round = 5;
        String modelName = "drink_xgb_deal_match_model";

        String xgb_demo_url = "http://10.169.161.22:8080/test/invokeDirectly?interpreter=python3.7_common&path=algo_pack.a0c5e812f19.demo.demo&method=xgb_demo";
        String xgb_predict_matrix_url = "http://10.169.161.22:8080/test/invokeDirectly?interpreter=python3.7_common&path=algo_pack.a0c5e812f19.demo.demo&method=xgb_predict_matrix";

        List<Integer> targetBatchSizes = Arrays.asList(10, 100, 200, 400, 600, 800, 1000, 1500, 2000, 2500, 3000, 4000);
//        List<Integer> targetBatchSizes = Arrays.asList(10, 100);
        List<Integer> allC1Cost = new ArrayList<>();
        List<Integer> allC2Cost = new ArrayList<>();
        for (int targetBatchSize : targetBatchSizes) {
            System.out.println(targetBatchSize);
            List<Integer> c1 = new ArrayList<>();
            List<Integer> c2 = new ArrayList<>();
            for (int i = 0; i < round; i++) {
                Pair<String, String> pair = genStr(featureNames, targetBatchSize, modelName);

                String resp1 = sendRequest(xgb_predict_matrix_url, pair.getLeft());
                c1.add(JSONObject.parseObject(resp1).getIntValue("cost"));
                String resp2 = sendRequest(xgb_demo_url, pair.getRight());
                c2.add(JSONObject.parseObject(resp2).getIntValue("cost"));

                assert Objects.equals(JSONObject.parseObject(resp1).getIntValue("data"), JSONObject.parseObject(resp2).getIntValue("data"));
            }
            allC1Cost.add(c1.stream().mapToInt(Integer::intValue).sum() / round);
            allC2Cost.add(c2.stream().mapToInt(Integer::intValue).sum() / round);
        }
        System.out.println("result:");
        for (int i = 0; i < targetBatchSizes.size(); i++) {
            int batchSize = targetBatchSizes.get(i);
            System.out.println(batchSize + "\t" + allC1Cost.get(i) + "\t" + allC2Cost.get(i));
        }
    }

    public static String sendRequest(String urlStr, String body) throws IOException {
        URL url = new URL(urlStr);
        URLConnection con = url.openConnection();
        HttpURLConnection http = (HttpURLConnection) con;
        http.setRequestMethod("POST"); // PUT is another valid option
        http.setDoOutput(true);

        byte[] out = body.getBytes(StandardCharsets.UTF_8);
        int length = out.length;

        http.setFixedLengthStreamingMode(length);
        http.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
        http.connect();
        try (OutputStream os = http.getOutputStream()) {
            os.write(out);
        }
        InputStream inputStream = http.getInputStream();
        String response = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        return response;
    }

    private static Pair<String, String> genStr(List<String> featureNames, int targetBatchSize, String modelName) {
        List<List<Double>> featureValList = new ArrayList<>();
        List<Map<String, Double>> featureValMapList = new ArrayList<>();
        for (int i = 0; i < targetBatchSize; i++) {
            Map<String, Double> featureValMap = new LinkedHashMap<>();
            List<Double> featureVals = new ArrayList<>();
            for (String featureName : featureNames) {
                Double val = RandomUtils.nextDouble(0, 1);
                featureValMap.put(featureName, val);
                featureVals.add(val);
            }
            featureValList.add(featureVals);
            featureValMapList.add(featureValMap);
        }
        return Pair.of(
                JSONObject.toJSONString(ImmutableMap.of("model_name", modelName, "req", JSONObject.toJSONString(featureValList))),
                JSONObject.toJSONString(ImmutableMap.of("model_name", modelName, "req", JSONObject.toJSONString(featureValMapList)))
        );
    }

    public void testStrLength() throws IOException {
        String str = "[[0.01852959580719471],[0.06266126036643982],[0.034068211913108826],[0.07369405776262283],[0.08342963457107544],[0.04499021917581558],[0.04867168143391609],[0.050501205027103424],[0.05636299401521683],[0.07892291992902756],[0.0337260477244854],[0.021835964173078537],[0.0726667121052742],[0.09377596527338028],[0.020720092579722404],[0.0492376908659935],[0.019678166136145592],[0.029384169727563858],[0.10632410645484924],[0.0364314541220665],[0.016462167724967003],[0.015031998977065086],[0.02812161296606064],[0.030233750119805336],[0.10026554763317108],[0.018076550215482712],[0.07060271501541138],[0.030590521171689034],[0.028756411746144295],[0.045841291546821594],[0.016988089308142662],[0.03427676111459732],[0.040875889360904694],[0.035560011863708496],[0.03939060494303703],[0.03621472045779228],[0.024419398978352547],[0.14714166522026062],[0.02273673005402088],[0.0388445146381855],[0.034472089260816574],[0.055716462433338165],[0.02514205314218998],[0.03304218873381615],[0.031411062926054],[0.020905492827296257],[0.046474162489175797],[0.03863322362303734],[0.15966781973838806],[0.0310314130038023],[0.03698822855949402],[0.03589756414294243],[0.055716462433338165],[0.02049337886273861],[0.07912307232618332],[0.0814749076962471],[0.025785241276025772],[0.08829466253519058],[0.06405918300151825],[0.03872280940413475],[0.0329313650727272],[0.03307148814201355],[0.018262671306729317],[0.03160623461008072],[0.03249868005514145],[0.03792325034737587],[0.06133400648832321],[0.09066957235336304],[0.06860734522342682],[0.022126786410808563],[0.026739632710814476],[0.05008174106478691],[0.03389573097229004],[0.060513339936733246],[0.05203400179743767],[0.0592212937772274],[0.058734387159347534],[0.021683860570192337],[0.022696275264024734],[0.03048744797706604],[0.03160623461008072],[0.03257308527827263],[0.055567190051078796],[0.027845952659845352],[0.06337838619947433],[0.04407624527812004],[0.04685481637716293],[0.02812161296606064],[0.06772994250059128],[0.021495990455150604],[0.05576205998659134],[0.03587616607546806],[0.05633610486984253],[0.07184787839651108],[0.14592377841472626],[0.17753025889396667],[0.03665534406900406],[0.020952709019184113],[0.04838045313954353],[0.036143846809864044],[0.023472290486097336],[0.021813416853547096],[0.11240971088409424],[0.02880917303264141],[0.041512083262205124],[0.028091343119740486],[0.07681038975715637],[0.04772248864173889],[0.026967797428369522],[0.021344546228647232],[0.01749708503484726],[0.018262671306729317],[0.03032059781253338],[0.018970420584082603],[0.02420791983604431],[0.07400797307491302],[0.05435851588845253],[0.09623871743679047],[0.026612820103764534],[0.054356273263692856],[0.039896104484796524],[0.044408854097127914],[0.04338933527469635],[0.019894301891326904],[0.13797378540039062],[0.04823855683207512],[0.0590079240500927],[0.042121756821870804],[0.06805477291345596],[0.09266997873783112],[0.025165321305394173],[0.02246725745499134],[0.06088372319936752],[0.07494990527629852],[0.06637394428253174],[0.03150124102830887],[0.11126839369535446],[0.030923359096050262],[0.06645379960536957],[0.03214571624994278],[0.06940105557441711],[0.022882072255015373],[0.09570258855819702],[0.04773703217506409],[0.041260406374931335],[0.09071917831897736],[0.06743193417787552],[0.022395139560103416],[0.02730800397694111],[0.031193582341074944],[0.09342992305755615],[0.049521006643772125],[0.023017922416329384],[0.050432268530130386],[0.06642860174179077],[0.02536599338054657],[0.055668991059064865],[0.03134892135858536],[0.04251237213611603],[0.029057476669549942],[0.059005167335271835],[0.021209120750427246],[0.07991810888051987],[0.019314611330628395],[0.038328852504491806],[0.01748487539589405],[0.07452051341533661],[0.0244233850389719],[0.06790054589509964],[0.05163548141717911],[0.05632404610514641],[0.02230946533381939],[0.026692448183894157],[0.05949762463569641],[0.05931347608566284],[0.11565437912940979],[0.04481293633580208],[0.038770850747823715],[0.05460089072585106],[0.040174469351768494],[0.042217694222927094],[0.0816173180937767],[0.2192533314228058],[0.04189491644501686],[0.019867105409502983],[0.08542830497026443],[0.020145760849118233],[0.025736210867762566],[0.02975190430879593],[0.04483519122004509],[0.021493395790457726],[0.018766839057207108],[0.028567735105752945],[0.05087444186210632],[0.021750174462795258],[0.021331654861569405],[0.06815315037965775],[0.019338179379701614],[0.20753459632396698],[0.04091281443834305],[0.05744055286049843],[0.044751476496458054],[0.03735750913619995],[0.1550358086824417],[0.015543056651949883],[0.04867318272590637],[0.04999392107129097],[0.045166365802288055],[0.0637848898768425],[0.017984744161367416],[0.02587653510272503],[0.03988991677761078],[0.07488440722227097],[0.028519801795482635],[0.017578423023223877],[0.055891554802656174],[0.11411526799201965],[0.09870252758264542],[0.04542794078588486],[0.031726203858852386],[0.019443055614829063],[0.11117276549339294],[0.03413064777851105],[0.06394558399915695],[0.029351919889450073],[0.04053035378456116],[0.020928537473082542],[0.031171036884188652],[0.025222212076187134],[0.020054884254932404],[0.051608357578516006],[0.022388868033885956],[0.06280816346406937],[0.1265113651752472],[0.027733702212572098],[0.03708887845277786],[0.02708953619003296],[0.03540405258536339],[0.052115678787231445],[0.03363543003797531],[0.030974887311458588],[0.0323479138314724],[0.021443579345941544],[0.024666594341397285],[0.041512083262205124],[0.0611257329583168],[0.0379144512116909],[0.09200144559144974],[0.07013007998466492],[0.07765597105026245],[0.02722909301519394],[0.034846510738134384],[0.17311234772205353],[0.07600238174200058],[0.017961306497454643],[0.14701391756534576],[0.055891554802656174],[0.02544277347624302],[0.0580715611577034],[0.04179082438349724],[0.03765879571437836],[0.020997729152441025],[0.02274572290480137],[0.13180269300937653],[0.023946300148963928],[0.018978437408804893],[0.018948331475257874],[0.04083528369665146],[0.08234727382659912],[0.03339686617255211],[0.09743794053792953],[0.022656388580799103],[0.021524695679545403],[0.13006603717803955],[0.02658751606941223],[0.024916227906942368],[0.07716257125139236],[0.04219583421945572],[0.07389707118272781],[0.030691232532262802],[0.049303505569696426],[0.07179490476846695],[0.06868601590394974],[0.12617310881614685],[0.044101595878601074],[0.02726832963526249],[0.017578423023223877],[0.025440683588385582],[0.0323479138314724],[0.043549247086048126],[0.02507491037249565],[0.04534108191728592],[0.016076182946562767],[0.03202720731496811],[0.10053105652332306],[0.027649404481053352],[0.024147136136889458],[0.09033654630184174],[0.11753710359334946],[0.025426875799894333],[0.027501849457621574],[0.05010667443275452],[0.049792610108852386],[0.08527683466672897],[0.017984744161367416],[0.04793675243854523],[0.0285000279545784],[0.01837630197405815],[0.0321001298725605],[0.12169954925775528],[0.08634694665670395],[0.015543056651949883],[0.024111509323120117],[0.027020413428544998],[0.048530109226703644],[0.020721370354294777],[0.040282346308231354],[0.02340308018028736],[0.08624783903360367],[0.04251237213611603],[0.020997729152441025],[0.024005651473999023],[0.03052784688770771],[0.06026362627744675],[0.04476422071456909],[0.03746691346168518],[0.08970977365970612],[0.027498316019773483],[0.1337260901927948],[0.01770266890525818],[0.040041372179985046],[0.054577890783548355],[0.0402192547917366],[0.04188719764351845],[0.0339612253010273],[0.04473522678017616],[0.059241361916065216],[0.018330736085772514],[0.05581074208021164],[0.06814129650592804],[0.07130289822816849],[0.04668644815683365],[0.024279577657580376],[0.021363073959946632],[0.052897464483976364],[0.024078460410237312],[0.059990301728248596],[0.0278704185038805],[0.04677099734544754],[0.02983951009809971],[0.08868657052516937],[0.06425733864307404],[0.02522558718919754],[0.03735750913619995],[0.05120651423931122],[0.024960240349173546],[0.03705863654613495],[0.014548692852258682],[0.0531771145761013],[0.05349085107445717],[0.05945521593093872],[0.028727419674396515],[0.02693176455795765],[0.02987668476998806],[0.22837695479393005],[0.18648874759674072],[0.14333367347717285],[0.03138674795627594],[0.02757287211716175],[0.1354368031024933],[0.24490758776664734],[0.02344229444861412],[0.016821136698126793],[0.1254224181175232],[0.03355797380208969],[0.08451370894908905],[0.018526723608374596],[0.020909231156110764],[0.024251114577054977],[0.111644446849823],[0.023802515119314194],[0.028032952919602394],[0.04883696511387825],[0.0317750908434391],[0.0323479138314724],[0.08474903553724289],[0.030883610248565674],[0.03355797380208969],[0.04738042131066322],[0.040038228034973145],[0.03008566051721573],[0.052249375730752945],[0.06668127328157425],[0.03674694150686264],[0.06261689960956573],[0.07981130480766296],[0.029024962335824966],[0.08399666845798492],[0.03355797380208969],[0.02692117728292942],[0.04680318012833595],[0.025403225794434547],[0.017734140157699585],[0.07106570899486542],[0.05067546293139458],[0.024868182837963104],[0.08868657052516937],[0.051608357578516006],[0.03016376867890358],[0.08213531225919724],[0.03683796525001526],[0.038154467940330505],[0.15451720356941223],[0.02188502438366413],[0.05153859406709671],[0.020720092579722404],[0.04789603874087334],[0.05367404222488403],[0.028214959427714348],[0.052493494004011154],[0.03160623461008072],[0.09017842262983322],[0.032253969460725784],[0.033412326127290726],[0.029473699629306793],[0.06264660507440567],[0.030519427731633186],[0.06358605623245239],[0.01850680261850357],[0.026497183367609978],[0.019597692415118217],[0.06285233050584793],[0.0892806351184845],[0.025623003020882607],[0.05703045800328255],[0.03842860832810402],[0.022126786410808563],[0.02087990753352642],[0.08191962540149689],[0.06362029165029526],[0.07328709214925766],[0.04799777641892433],[0.04032215476036072],[0.026141319423913956],[0.034068211913108826],[0.046776968985795975],[0.020888369530439377],[0.018321309238672256],[0.054195284843444824],[0.025358572602272034],[0.025190114974975586],[0.10433892160654068],[0.0288909412920475],[0.0599968247115612],[0.06435369700193405],[0.016922876238822937],[0.021835964173078537],[0.024251114577054977],[0.022450394928455353],[0.09659513086080551],[0.03306043520569801],[0.019383016973733902],[0.06189506873488426],[0.034200653433799744],[0.04015764594078064],[0.02049337886273861],[0.016922876238822937],[0.05594717338681221],[0.03333611786365509],[0.028649630025029182],[0.04485312104225159],[0.034066423773765564],[0.023174133151769638],[0.017578423023223877],[0.03222798556089401],[0.03281160071492195],[0.0172917228192091],[0.018366027623414993],[0.028094690293073654],[0.07332924753427505],[0.021815670654177666],[0.024251114577054977],[0.02239226922392845],[0.06754511594772339],[0.044894006103277206],[0.04115960747003555],[0.027816222980618477],[0.07336688041687012],[0.07567029446363449],[0.026659687981009483],[0.015031998977065086],[0.06593966484069824],[0.023570112884044647],[0.03913499414920807],[0.04146939143538475],[0.045709967613220215],[0.029466841369867325],[0.04543686658143997],[0.01890951208770275],[0.03826068341732025]]";

        List<Double> doubles = new ArrayList<>();
        JSONArray array = JSONArray.parseArray(str);
        for (int i = 0; i < array.size(); i++) {
            JSONArray array1 = array.getJSONArray(i);
            Double aDouble = array1.getDouble(0);
            doubles.add(aDouble);
        }
        MessageBufferPacker packer = MessagePack.newDefaultBufferPacker();
        packer.packArrayHeader(doubles.size());
        for (double e : doubles) {
            packer.packDouble(e);
        }
        byte[] inputBytes = packer.toByteArray();
        String encodeToString = Base64.getEncoder().encodeToString(inputBytes);
        System.out.println(str.length() + "," + encodeToString.length());
    }
}
