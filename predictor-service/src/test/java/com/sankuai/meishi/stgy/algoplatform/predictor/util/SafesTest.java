package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import org.junit.Test;
import org.junit.Assert;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

public class SafesTest {

    /**
     * 测试当输入的list为null时，应返回一个空的List
     */
    @Test
    public void testOf_NullList() throws Throwable {
        // arrange
        List<Object> inputList = null;
        // act
        List<Object> result = Safes.of(inputList);
        // assert
        Assert.assertNotNull("返回的List不应为null", result);
        Assert.assertTrue("返回的List应为空", result.isEmpty());
    }

    /**
     * 测试当输入的map为null时，应返回一个空的Map
     */
    @Test
    public void testOf_NullMap() throws Throwable {
        // arrange
        Map<String, Object> inputMap = null;
        // act
        Map<String, Object> result = Safes.of(inputMap);
        // assert
        Assert.assertNotNull("返回的Map不应为null", result);
        Assert.assertTrue("返回的Map应为空", result.isEmpty());
    }

    /**
     * 测试当输入的list非null且为空时，应返回输入的list
     */
    @Test
    public void testOf_EmptyNonNullList() throws Throwable {
        // arrange
        List<Object> inputList = new ArrayList<>();
        // act
        List<Object> result = Safes.of(inputList);
        // assert
        Assert.assertSame("返回的List应与输入的List为同一个对象", inputList, result);
        Assert.assertTrue("返回的List应为空", result.isEmpty());
    }

    /**
     * 测试当输入的list非null且包含元素时，应返回输入的list
     */
    @Test
    public void testOf_NonEmptyNonNullList() throws Throwable {
        // arrange
        List<Object> inputList = new ArrayList<>();
        Object element = new Object();
        inputList.add(element);
        // act
        List<Object> result = Safes.of(inputList);
        // assert
        Assert.assertSame("返回的List应与输入的List为同一个对象", inputList, result);
        Assert.assertFalse("返回的List不应为空", result.isEmpty());
        Assert.assertEquals("返回的List的大小应为1", 1, result.size());
        Assert.assertSame("返回的List中的元素应与输入的List中的元素为同一个对象", element, result.get(0));
    }
}
