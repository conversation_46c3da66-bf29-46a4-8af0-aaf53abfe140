package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import static org.mockito.Mockito.*;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackage;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.AlgoPackageDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoPackagePo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class AlgoPackageServiceImpl_Refresh_1_Test {

    @InjectMocks
    private AlgoPackageServiceImpl algoPackageService;

    @Mock
    private AlgoPackageDao algoPackageDao;

    private List<AlgoPackagePo> packagePos;

    @Before
    public void setUp() {
        packagePos = new ArrayList<>();
        AlgoPackagePo po1 = new AlgoPackagePo();
        po1.setId(1L);
        po1.setVersion("1.0");
        po1.setNote("note1");
        po1.setOwnerMis("owner1");
        po1.setRuntime("runtime1");
        po1.setModulePath("modulePath1");
        packagePos.add(po1);
        AlgoPackagePo po2 = new AlgoPackagePo();
        po2.setId(2L);
        po2.setVersion("2.0");
        po2.setNote("note2");
        po2.setOwnerMis("owner2");
        po2.setRuntime("runtime2");
        po2.setModulePath("modulePath2");
        packagePos.add(po2);
    }

    @Test
    public void testRefreshWhenNoValidPackages() throws Throwable {
        when(algoPackageDao.getValidPackages()).thenReturn(new ArrayList<>());
        int result = algoPackageService.refresh(false);
        assertEquals(0, result);
    }

    @Test(expected = RuntimeException.class)
    public void testRefreshWhenUpdatePackageThrowErrorAndThrowErrorIsTrue() throws Throwable {
        when(algoPackageDao.getValidPackages()).thenReturn(packagePos);
        AlgoPackage algoPackage = mock(AlgoPackage.class);
        algoPackageService.refresh(true);
    }

    @Test
    public void testRefreshWhenSomePackagesNeedUpdateAndSomeAreRemoved() throws Throwable {
        when(algoPackageDao.getValidPackages()).thenReturn(Arrays.asList(packagePos.get(0)));
        AlgoPackage algoPackage = mock(AlgoPackage.class);
        int result = algoPackageService.refresh(false);
        assertEquals(0, result);
    }
}
