package com.sankuai.meishi.stgy.algoplatform.predictor.service.util;

import org.junit.Test;
import static org.junit.Assert.*;
import java.nio.IntBuffer;

public class TritonUtil_ToArrayTest {

    // 其他测试方法保持不变
    // ...
    /**
     * 测试 IntBuffer 有可访问的底层数组且无偏移量
     */
    @Test
    public void testToArray_NoOffset() throws Throwable {
        // arrange
        int[] expected = new int[] { 1, 2, 3 };
        IntBuffer intBuffer = IntBuffer.wrap(expected);
        // act
        int[] actual = TritonUtil.toArray(intBuffer);
        // assert
        assertArrayEquals(expected, actual);
    }

    /**
     * 测试 IntBuffer 没有可访问的底层数组
     */
    @Test
    public void testToArray_NoAccessibleArray() throws Throwable {
        // arrange
        int[] expected = new int[] { 1, 2, 3 };
        IntBuffer intBuffer = IntBuffer.allocate(3);
        intBuffer.put(expected);
        intBuffer.flip();
        // act
        int[] actual = TritonUtil.toArray(intBuffer);
        // assert
        assertArrayEquals(expected, actual);
    }

    /**
     * 测试 IntBuffer 为 null 时抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testToArray_NullIntBuffer() throws Throwable {
        // arrange
        IntBuffer intBuffer = null;
        // act
        TritonUtil.toArray(intBuffer);
        // assert is handled by the expected exception
    }
}
