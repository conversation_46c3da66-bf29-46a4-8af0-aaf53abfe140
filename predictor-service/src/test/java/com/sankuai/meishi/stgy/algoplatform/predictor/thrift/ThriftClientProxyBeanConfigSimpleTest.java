package com.sankuai.meishi.stgy.algoplatform.predictor.thrift;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * ThriftClientProxyBeanConfig 简化单元测试（不使用PowerMock）
 * 主要测试缓存机制、销毁逻辑和参数处理
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@RunWith(MockitoJUnitRunner.class)
public class ThriftClientProxyBeanConfigSimpleTest {

    private ThriftClientProxyBeanConfig thriftClientProxyBeanConfig;

    @Mock
    private ThriftClientProxy mockThriftClientProxy;

    @Before
    public void setUp() {
        thriftClientProxyBeanConfig = new ThriftClientProxyBeanConfig();
        
        // 清空静态缓存
        ThriftClientProxyBeanConfig.serviceClient.clear();
    }

    /**
     * 测试从缓存获取代理
     */
    @Test
    public void testCreateThriftProxy_FromCache() throws Exception {
        // arrange
        ThriftServiceInfo serviceInfo = createThriftServiceInfo("com.sankuai.test", "com.test.ThriftService", "testMethod", 5000, null, null, null);
        String cacheKey = "com.sankuai.test_com.test.ThriftService_null_null_null_5000";
        
        // 预先放入缓存
        ThriftClientProxyBeanConfig.serviceClient.put(cacheKey, mockThriftClientProxy);

        // act
        ThriftClientProxy result = thriftClientProxyBeanConfig.createThriftProxy(serviceInfo);

        // assert
        assertNotNull(result);
        assertEquals(mockThriftClientProxy, result);
        
        // 验证缓存中仍然存在
        assertTrue(ThriftClientProxyBeanConfig.serviceClient.containsKey(cacheKey));
        assertEquals(mockThriftClientProxy, ThriftClientProxyBeanConfig.serviceClient.get(cacheKey));
    }

    /**
     * 测试缓存key生成逻辑 - 使用IP和端口
     */
    @Test
    public void testCacheKeyGeneration_WithIpAndPort() throws Exception {
        // arrange
        ThriftServiceInfo serviceInfo = createThriftServiceInfo(null, "com.test.ThriftService", "testMethod", 5000, null, "127.0.0.1", "8080");
        String expectedKey = "null_com.test.ThriftService_null_127.0.0.1_8080_5000";
        
        // 预先放入缓存
        ThriftClientProxyBeanConfig.serviceClient.put(expectedKey, mockThriftClientProxy);

        // act
        ThriftClientProxy result = thriftClientProxyBeanConfig.createThriftProxy(serviceInfo);

        // assert
        assertNotNull(result);
        assertEquals(mockThriftClientProxy, result);
    }

    /**
     * 测试缓存key生成逻辑 - 使用appKey和端口
     */
    @Test
    public void testCacheKeyGeneration_WithAppKeyAndPort() throws Exception {
        // arrange
        ThriftServiceInfo serviceInfo = createThriftServiceInfo("com.sankuai.test", "com.test.ThriftService", "testMethod", 5000, null, null, "8080");
        String expectedKey = "com.sankuai.test_com.test.ThriftService_null_null_8080_5000";
        
        // 预先放入缓存
        ThriftClientProxyBeanConfig.serviceClient.put(expectedKey, mockThriftClientProxy);

        // act
        ThriftClientProxy result = thriftClientProxyBeanConfig.createThriftProxy(serviceInfo);

        // assert
        assertNotNull(result);
        assertEquals(mockThriftClientProxy, result);
    }

    /**
     * 测试缓存key生成逻辑 - 所有字段为null
     */
    @Test
    public void testCacheKeyGeneration_AllFieldsNull() throws Exception {
        // arrange
        ThriftServiceInfo serviceInfo = createThriftServiceInfo(null, null, null, null, null, null, null);
        String expectedKey = "null_null_null_null_null_null";
        
        // 预先放入缓存
        ThriftClientProxyBeanConfig.serviceClient.put(expectedKey, mockThriftClientProxy);

        // act
        ThriftClientProxy result = thriftClientProxyBeanConfig.createThriftProxy(serviceInfo);

        // assert
        assertNotNull(result);
        assertEquals(mockThriftClientProxy, result);
    }

    /**
     * 测试销毁代理 - 通过key
     */
    @Test
    public void testDestroy_ByKey() throws Exception {
        // arrange
        String proxyKey = "test_key";
        ThriftClientProxyBeanConfig.serviceClient.put(proxyKey, mockThriftClientProxy);
        assertEquals(1, ThriftClientProxyBeanConfig.serviceClient.size());

        // act
        thriftClientProxyBeanConfig.destroy(proxyKey);

        // assert
        assertFalse(ThriftClientProxyBeanConfig.serviceClient.containsKey(proxyKey));
        assertEquals(0, ThriftClientProxyBeanConfig.serviceClient.size());
        verify(mockThriftClientProxy).destroy();
    }

    /**
     * 测试销毁代理 - key不存在
     */
    @Test
    public void testDestroy_KeyNotExists() throws Exception {
        // arrange
        String proxyKey = "non_existing_key";
        assertEquals(0, ThriftClientProxyBeanConfig.serviceClient.size());

        // act
        thriftClientProxyBeanConfig.destroy(proxyKey);

        // assert
        assertEquals(0, ThriftClientProxyBeanConfig.serviceClient.size());
        // 不应该调用destroy方法
        verify(mockThriftClientProxy, never()).destroy();
    }

    /**
     * 测试销毁代理 - 销毁时抛出异常
     */
    @Test
    public void testDestroy_DestroyException() throws Exception {
        // arrange
        String proxyKey = "test_key";
        ThriftClientProxyBeanConfig.serviceClient.put(proxyKey, mockThriftClientProxy);
        doThrow(new RuntimeException("Destroy failed")).when(mockThriftClientProxy).destroy();

        // act
        thriftClientProxyBeanConfig.destroy(proxyKey);

        // assert
        assertFalse(ThriftClientProxyBeanConfig.serviceClient.containsKey(proxyKey));
        verify(mockThriftClientProxy).destroy();
    }

    /**
     * 测试销毁代理 - 通过ThriftServiceInfo
     */
    @Test
    public void testDestroy_ByThriftServiceInfo() throws Exception {
        // arrange
        ThriftServiceInfo serviceInfo = createThriftServiceInfo("com.sankuai.test", "com.test.ThriftService", "testMethod", 5000, "test-cell", "127.0.0.1", "8080");
        String expectedKey = "com.sankuai.test_com.test.ThriftService_test-cell_127.0.0.1_8080_5000";
        ThriftClientProxyBeanConfig.serviceClient.put(expectedKey, mockThriftClientProxy);

        // act
        thriftClientProxyBeanConfig.destroy(serviceInfo);

        // assert
        assertFalse(ThriftClientProxyBeanConfig.serviceClient.containsKey(expectedKey));
        verify(mockThriftClientProxy).destroy();
    }

    /**
     * 测试多个不同服务的缓存
     */
    @Test
    public void testMultipleServiceCaching() throws Exception {
        // arrange
        ThriftServiceInfo serviceInfo1 = createThriftServiceInfo("com.sankuai.test1", "com.test.Service1", "testMethod1", 5000, "cell1", null, null);
        ThriftServiceInfo serviceInfo2 = createThriftServiceInfo("com.sankuai.test2", "com.test.Service2", "testMethod2", 6000, "cell2", null, null);
        
        ThriftClientProxy proxy1 = mock(ThriftClientProxy.class);
        ThriftClientProxy proxy2 = mock(ThriftClientProxy.class);
        
        String key1 = "com.sankuai.test1_com.test.Service1_cell1_null_null_5000";
        String key2 = "com.sankuai.test2_com.test.Service2_cell2_null_null_6000";
        
        // 预先放入缓存
        ThriftClientProxyBeanConfig.serviceClient.put(key1, proxy1);
        ThriftClientProxyBeanConfig.serviceClient.put(key2, proxy2);

        // act
        ThriftClientProxy result1 = thriftClientProxyBeanConfig.createThriftProxy(serviceInfo1);
        ThriftClientProxy result2 = thriftClientProxyBeanConfig.createThriftProxy(serviceInfo2);

        // assert
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotEquals(result1, result2);
        assertEquals(proxy1, result1);
        assertEquals(proxy2, result2);
        
        // 验证缓存
        assertEquals(2, ThriftClientProxyBeanConfig.serviceClient.size());
        assertTrue(ThriftClientProxyBeanConfig.serviceClient.containsKey(key1));
        assertTrue(ThriftClientProxyBeanConfig.serviceClient.containsKey(key2));
    }

    /**
     * 测试相同服务信息只创建一次
     */
    @Test
    public void testSameServiceOnlyCreateOnce() throws Exception {
        // arrange
        ThriftServiceInfo serviceInfo = createThriftServiceInfo("com.sankuai.test", "com.test.ThriftService", "testMethod", 5000, null, null, null);
        String cacheKey = "com.sankuai.test_com.test.ThriftService_null_null_null_5000";
        
        // 预先放入缓存
        ThriftClientProxyBeanConfig.serviceClient.put(cacheKey, mockThriftClientProxy);

        // act
        ThriftClientProxy result1 = thriftClientProxyBeanConfig.createThriftProxy(serviceInfo);
        ThriftClientProxy result2 = thriftClientProxyBeanConfig.createThriftProxy(serviceInfo);

        // assert
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(result1, result2);
        assertEquals(mockThriftClientProxy, result1);
        assertEquals(mockThriftClientProxy, result2);
        
        // 验证缓存中只有一个实例
        assertEquals(1, ThriftClientProxyBeanConfig.serviceClient.size());
    }

    /**
     * 测试缓存清空功能
     */
    @Test
    public void testCacheClear() throws Exception {
        // arrange
        String cacheKey = "com.sankuai.test_com.test.ThriftService_null_null_null_5000";
        
        // 预先放入缓存
        ThriftClientProxyBeanConfig.serviceClient.put(cacheKey, mockThriftClientProxy);
        assertEquals(1, ThriftClientProxyBeanConfig.serviceClient.size());

        // act
        ThriftClientProxyBeanConfig.serviceClient.clear();

        // assert
        assertEquals(0, ThriftClientProxyBeanConfig.serviceClient.size());
        assertFalse(ThriftClientProxyBeanConfig.serviceClient.containsKey(cacheKey));
    }

    /**
     * 测试批量销毁代理
     */
    @Test
    public void testBatchDestroy() throws Exception {
        // arrange
        ThriftClientProxy proxy1 = mock(ThriftClientProxy.class);
        ThriftClientProxy proxy2 = mock(ThriftClientProxy.class);
        ThriftClientProxy proxy3 = mock(ThriftClientProxy.class);
        
        ThriftClientProxyBeanConfig.serviceClient.put("key1", proxy1);
        ThriftClientProxyBeanConfig.serviceClient.put("key2", proxy2);
        ThriftClientProxyBeanConfig.serviceClient.put("key3", proxy3);
        assertEquals(3, ThriftClientProxyBeanConfig.serviceClient.size());

        // act
        thriftClientProxyBeanConfig.destroy("key1");
        thriftClientProxyBeanConfig.destroy("key2");
        thriftClientProxyBeanConfig.destroy("key3");

        // assert
        assertEquals(0, ThriftClientProxyBeanConfig.serviceClient.size());
        verify(proxy1).destroy();
        verify(proxy2).destroy();
        verify(proxy3).destroy();
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建ThriftServiceInfo测试对象
     */
    private ThriftServiceInfo createThriftServiceInfo(String appKey, String interfaceName, String methodName, Integer timeOut, String cell, String ip, String port) {
        ThriftServiceInfo serviceInfo = new ThriftServiceInfo();
        serviceInfo.setAppKey(appKey);
        serviceInfo.setInterfaceName(interfaceName);
        serviceInfo.setMethodName(methodName);
        serviceInfo.setTimeOut(timeOut);
        serviceInfo.setCell(cell);
        serviceInfo.setIp(ip);
        serviceInfo.setPort(port);
        return serviceInfo;
    }
}
