package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.dto.PredictModelDto;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import java.util.Arrays;
import java.util.Collection;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(Lion.class)
public class LogTools_LogNoCacheTest {

    @Mock
    private ConfigRepository configRepository;

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(Lion.class);
        when(Lion.getConfigRepository()).thenReturn(configRepository);
    }

    @Test
    public void testLogNoCacheSwitchOff() throws Throwable {
        PredictModelDto predictModelDto = new PredictModelDto();
        predictModelDto.setModel_name("testModel");
        Collection<String> noCacheKey = Arrays.asList("key1", "key2");
        when(configRepository.getBooleanValue(eq(LionKeys.FEATURE_NO_CACHE_LOG_SWITCH), eq(false))).thenReturn(false);
        LogTools.logNoCache(predictModelDto, noCacheKey);
        verify(configRepository, times(1)).getBooleanValue(eq(LionKeys.FEATURE_NO_CACHE_LOG_SWITCH), eq(false));
    }

    @Test
    public void testLogNoCacheSwitchOnNoException() throws Throwable {
        PredictModelDto predictModelDto = new PredictModelDto();
        predictModelDto.setModel_name("testModel");
        Collection<String> noCacheKey = Arrays.asList("key1", "key2");
        when(configRepository.getBooleanValue(eq(LionKeys.FEATURE_NO_CACHE_LOG_SWITCH), eq(false))).thenReturn(true);
        LogTools.logNoCache(predictModelDto, noCacheKey);
        verify(configRepository, times(1)).getBooleanValue(eq(LionKeys.FEATURE_NO_CACHE_LOG_SWITCH), eq(false));
    }

    @Test
    public void testLogNoCacheSwitchOnWithException() throws Throwable {
        PredictModelDto predictModelDto = new PredictModelDto();
        predictModelDto.setModel_name("testModel");
        Collection<String> noCacheKey = Arrays.asList("key1", "key2");
        when(configRepository.getBooleanValue(eq(LionKeys.FEATURE_NO_CACHE_LOG_SWITCH), eq(false))).thenReturn(true);
        // Simulate an exception scenario by changing the model name to null
        predictModelDto.setModel_name(null);
        LogTools.logNoCache(predictModelDto, noCacheKey);
        verify(configRepository, times(1)).getBooleanValue(eq(LionKeys.FEATURE_NO_CACHE_LOG_SWITCH), eq(false));
    }
}
