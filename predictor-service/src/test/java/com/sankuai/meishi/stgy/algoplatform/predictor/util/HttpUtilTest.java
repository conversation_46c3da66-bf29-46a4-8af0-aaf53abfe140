package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.sankuai.oceanus.http.internal.HttpHeader;
import com.sankuai.oceanus.http.internal.TargetRequestEnv;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static com.sankuai.meishi.stgy.algoplatform.predictor.util.HttpUtil.OCTO_APP_KEY;

public class HttpUtilTest {
    @Test
    public void test() {
        String s = HttpUtil.httpGet("https://www.meituan.com/", null, null);
        assert !StringUtils.isEmpty(s);
    }

    @Test
    public void testParam() {
        Map<String, String> param = new HashMap<String, String>() {
            {
                put("bmlMatchSumPrefix", "bml_dj_mt_poi_approx_match");
                put("bmlMatchResultSumPrefix", "app_algoplatform_predictor_approximate_result");
            }
        };
        String s = HttpUtil.httpGet("https://bmlmatch.sankuai.com/cantor/dealmatch", param, null);
        Integer status = JSON.parseObject(s).getInteger("status");
        assert status == 1 || status == 0;
    }

    @Test
    public void testException() {
        String s = HttpUtil.httpGet("https://abc.abc.com/", null, null);
        assert s == null;
    }

    @Test
    public void testOctoNode() {
//        String url = "https://octoopenapi.vip.sankuai.com/v1/provider";
//        Map<String, String> param = new HashMap<String, String>() {
//            {
//                put("appkey", "com.sankuai.algoplatform.bmlserver");
//                put("type", "1");
////                put("env", "4"); //1: dev; 2: test; 3: stage; 4: prod。默认4PROD
//                put("status", "2");//节点状态：默认-1 -1 :所有 0:未启动 1: 启动中 2:正常 4:禁用
//            }
//        };
//        Map<String, String> header = ImmutableMap.of(HttpHeader.OCEANUS_RMOTE_APPKEY_HEADER, OCTO_APP_KEY,
//                HttpHeader.OCEANUS_TARGET_ENV_HEADER, TargetRequestEnv.ONLINE.getDesc());
//        String res = HttpUtil.httpGet(url, param, header);
//        JSONObject obj = JSON.parseObject(res);
//        assert obj != null;
        Map<String, String> param = new HashMap<String, String>() {
            {
                put("bmlMatchSumPrefix", "bml_dj_mt_poi_approx_match");
                put("bmlMatchResultSumPrefix", "app_algoplatform_predictor_approximate_result");
            }
        };
        String s = HttpUtil.httpGet("https://bmlmatch.sankuai.com/cantor/dealmatch", param,
                ImmutableMap.of("k", "v"));
        Integer status = JSON.parseObject(s).getInteger("status");
        assert status == 1 || status == 0;
    }

}
