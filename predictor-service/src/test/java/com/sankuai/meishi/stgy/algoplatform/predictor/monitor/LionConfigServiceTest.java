package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.meituan.mdp.boot.starter.config.consant.ConfigSource;
import com.meituan.mdp.boot.starter.config.vo.ConfigEvent;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class LionConfigServiceTest {

    @Mock
    private LionConfigService service;

    @Test
    public void testListenerCacheSwitch() {

        ConfigEvent event = ConfigEvent.builder()
                .key(LionKeys.PREDICT_CACHE_SWITCH)
                .group("default")
                .oldValue("true")
                .newValue("false")
                .source(ConfigSource.LION)
                .build();
        service.listenerCacheSwitch(event);
        assert event != null;
    }

    @Test
    public void testListenerCacheSConfig() {
        ConfigEvent event = ConfigEvent.builder()
                .key(LionKeys.PREDICT_CACHE_CONFIG)
                .group("default")
                .oldValue("[ { \"bizCode\": \"zb_dealMatching_test\", \"version\": \"1\", \"expireMinutes\": 2880 }, { \"bizCode\": \"dish_struct_recognize\", \"version\": \"1\", \"expireMinutes\": 2880 }, { \"bizCode\": \"zb_dealMatching\", \"version\": \"1\", \"expireMinutes\": 2880 }, { \"bizCode\": \"test\", \"version\": \"1\", \"expireMinutes\": 5 }, { \"bizCode\": \"test-mjf\", \"version\": \"1\", \"expireMinutes\": 43200 } ]")
                .newValue("[ { \"bizCode\": \"zb_dealMatching_test\", \"version\": \"1\", \"expireMinutes\": 2880 }, { \"bizCode\": \"dish_struct_recognize\", \"version\": \"1\", \"expireMinutes\": 2880 }, { \"bizCode\": \"zb_dealMatching\", \"version\": \"1\", \"expireMinutes\": 2880 }, { \"bizCode\": \"test\", \"version\": \"1\", \"expireMinutes\": 5 }, { \"bizCode\": \"test-mjf\", \"version\": \"1\", \"expireMinutes\": 43200 } ]")
                .source(ConfigSource.LION)
                .build();
        service.listenerCacheSConfig(event);
        assert event != null;
        assert !JSON.parseObject(event.getNewValue(), new TypeReference<List<Map<String, String>>>() {
        }).get(0).get("bizCode").equals("zb_dealMatching");
    }

    @Test
    public void testListenerPredictCacheSConfig() {
        ConfigEvent event = ConfigEvent.builder()
                .key(LionKeys.PREDICT_MODEL_CACHE_CONFIG)
                .group("default")
                .oldValue("{ \"cacheVersion\":\"1\", \"expireMinutes\":2880, \"globalCacheSwitch\":false, \"modelCacheBatchNum\":100, \"strategyId\":14, \"coupleCacheSwitch\": false, \"modelPredictCacheSwitch\": false, \"modelCacheBatchPutNum\": 100 }")
                .newValue("{ \"cacheVersion\":\"1\", \"expireMinutes\":2880, \"globalCacheSwitch\":false, \"modelCacheBatchNum\":100, \"strategyId\":14, \"coupleCacheSwitch\": false, \"modelPredictCacheSwitch\": false, \"modelCacheBatchPutNum\": 100 }")
                .source(ConfigSource.LION)
                .build();
        service.listenerPredictCacheSConfig(event);
        assert event != null;
        assert JSON.parseObject(event.getNewValue(), new TypeReference<Map<String, String>>() {
        }).get("globalCacheSwitch").equals("false");
    }
}
