package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.context.ApplicationContext;
import java.lang.reflect.Field;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

@RunWith(MockitoJUnitRunner.class)
public class SpringContextUtilsTest {

    @Mock
    private ApplicationContext applicationContext;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        // 通过反射设置 applicationContext 静态成员变量
        Field field = SpringContextUtils.class.getDeclaredField("applicationContext");
        field.setAccessible(true);
        field.set(null, applicationContext);
    }

    @Test
    public void testGetAliases_WithValidBeanName_ShouldReturnAliases() throws Exception {
        String beanName = "validBeanName";
        String[] expectedAliases = { "alias1", "alias2" };
        when(applicationContext.getAliases(beanName)).thenReturn(expectedAliases);
        String[] actualAliases = SpringContextUtils.getAliases(beanName);
        assertArrayEquals(expectedAliases, actualAliases);
    }

    @Test(expected = NoSuchBeanDefinitionException.class)
    public void testGetAliases_WithInvalidBeanName_ShouldThrowException() throws Exception {
        String beanName = "invalidBeanName";
        when(applicationContext.getAliases(beanName)).thenThrow(new NoSuchBeanDefinitionException(beanName));
        SpringContextUtils.getAliases(beanName);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGetAliases_WithNullBeanName_ShouldThrowException() throws Exception {
        when(applicationContext.getAliases(null)).thenThrow(new IllegalArgumentException());
        SpringContextUtils.getAliases(null);
    }
}
