package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictionQueryContext;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.TairClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.HorizonStrategyService;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.GroovyScriptFactory;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest(GroovyScriptFactory.class)
public class PredictionQueryAppServiceImpl_QueryPredictionsTest {

    @InjectMocks
    private PredictionQueryAppServiceImpl service;

    @Mock
    private TairClient tairClient;

    @Mock
    private HorizonStrategyService horizonStrategyService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(GroovyScriptFactory.class);
    }

    // ... 其他测试方法 ...
    @Test(expected = IllegalStateException.class)
    public void testQueryPredictionsByGroovyScriptInvalidMapOutput() {
        // arrange
        String script = "return ['invalid': 'data']";
        PredictionQueryContext context = new PredictionQueryContext();
        // 修改这里，让 runScript 返回非 Map 类型的对象
        when(GroovyScriptFactory.runScript(any(String.class), any(Map.class))).thenReturn("not a map");
        // act
        service.queryPredictionsByGroovyScript(script, context);
        // assert
        // Expected exception
    }

    @Test
    public void testQueryPredictionsByGroovyScriptNormal() {
        // arrange
        String script = "return ['data':[:], 'extra':[:]]";
        PredictionQueryContext context = new PredictionQueryContext();
        context.setBizCode("bizCode");
        context.setEntityIds(Collections.singletonList("entityId"));
        context.setReqExtra(new LinkedHashMap<>());
        Map<String, Object> scriptOutput = Collections.singletonMap("data", Collections.singletonMap("entityId", Collections.emptyList()));
        when(GroovyScriptFactory.runScript(any(String.class), any(Map.class))).thenReturn(scriptOutput);
        // act
        PredictionQueryContext result = service.queryPredictionsByGroovyScript(script, context);
        // assert
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getData().containsKey("entityId"));
    }

    // ... 其他测试方法保持不变 ...
    @Test(expected = IllegalStateException.class)
    public void testQueryPredictionsByGroovyScriptNonMapOutput() {
        // arrange
        String script = "return 'not a map'";
        PredictionQueryContext context = new PredictionQueryContext();
        when(GroovyScriptFactory.runScript(any(String.class), any(Map.class))).thenReturn("not a map");
        // act
        service.queryPredictionsByGroovyScript(script, context);
        // assert
        // Expected exception
    }
}
