package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackage;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import java.lang.reflect.Field;
import java.util.concurrent.ConcurrentHashMap;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import scala.collection.immutable.Stream;

import static org.mockito.Mockito.*;

/**
 * 测试 AlgoPackageServiceImpl 的 get 方法
 */
public class AlgoPackageServiceImpl_GetTest {

    @InjectMocks
    private AlgoPackageServiceImpl algoPackageService;

    @Spy
    private ConcurrentHashMap<Long, AlgoPackage> loadedPackages = new ConcurrentHashMap<>();

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        Field loadedPackagesField = AlgoPackageServiceImpl.class.getDeclaredField("loadedPackages");
        loadedPackagesField.setAccessible(true);
        loadedPackagesField.set(algoPackageService, loadedPackages);
    }

    /**
     * 测试 get 方法，当 id 对应的 AlgoPackage 存在时
     */
    @Test
    public void testGetPackageExists() {
        // arrange
        Long id = 1L;
        AlgoPackage expectedPackage = new AlgoPackage(id, "note", "ownerMis", "runtime", "modulePath", "version", Constants.ALGO_PACKAGE_GIT_URL);
        loadedPackages.put(id, expectedPackage);
        // act
        AlgoPackage result = algoPackageService.get(id);
        // assert
        assertNotNull("AlgoPackage should not be null", result);
        assertEquals("Retrieved AlgoPackage should match the expected one", expectedPackage, result);
    }

    /**
     * 测试 get 方法，当 id 对应的 AlgoPackage 不存在时
     */
    @Test
    public void testGetPackageNotExists() {
        // arrange
        Long id = 2L;
        // act
        AlgoPackage result = algoPackageService.get(id);
        // assert
        assertNull("AlgoPackage should be null when not found", result);
    }

    /**
     * 测试 get 方法，当 id 为 null 时
     */
    @Test
    public void testGetPackageIdIsNull() {
        // arrange
        Long id = null;
        doReturn(null).when(loadedPackages).get(id);
        // act
        AlgoPackage result = algoPackageService.get(id);
        // assert
        assertNull("AlgoPackage should be null when id is null", result);
    }
}
