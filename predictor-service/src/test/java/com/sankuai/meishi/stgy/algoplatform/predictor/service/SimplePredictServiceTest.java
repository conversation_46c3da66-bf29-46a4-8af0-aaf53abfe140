package com.sankuai.meishi.stgy.algoplatform.predictor.service;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.google.common.cache.LoadingCache;
import com.meituan.hadoop.afo.serving.client.thrift.MTThriftPredictClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.BaseTest;
import com.sankuai.meishi.stgy.algoplatform.predictor.TestCaseHelper;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackage;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoStrategy;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl.PredictAppServiceImpl;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.dto.PredictModelDto;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.impl.SimplePredictServiceImpl;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.TPredictServicePublish;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.tensorflow.framework.TensorProto;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @date 2024/1/9
 */
public class SimplePredictServiceTest {

//    @Autowired
    private SimplePredictService simplePredictService;

//    @MockBean
    private PredictAppServiceImpl predictAppService;

//    @MockBean
    private LoadingCache<String, MTThriftPredictClient> predictClientCache;

    private SimplePredictServiceImpl simplePredictServiceImpl = new SimplePredictServiceImpl();

//    @Before
    public void setUp() throws Exception {
        Mockito.when(predictAppService.getStrategyById(anyLong()))
                .thenReturn(new AlgoStrategy(1L, new AlgoPackage(), "", "") {
                    public Map<String, ?> run(Map<String, ?> req) {
                        return Collections.emptyMap();
                    }
                });
        Mockito.when(predictClientCache.get(anyString()))
                .thenReturn(new MTThriftPredictClient() {
                    @Override
                    public Map<String, TensorProto> predict(Map<String, TensorProto> features) throws Exception {
                        return Collections.emptyMap();
                    }
                });
    }
    
    private List<List<String>> buildInput() {
        List<List<String>> input = new ArrayList<>();
        AtomicInteger ai1 = new AtomicInteger(500);
        AtomicInteger ai2 = new AtomicInteger(1267);
        for (int i = 0; i < 123; i++) {
            input.add(Lists.newArrayList(String.valueOf(ai1.getAndIncrement()), String.valueOf(ai2.getAndIncrement())));
        }
        return input;
    }

//    @Test
    public void testPredictWithCache() {
        PredictModelDto predictModelDto = new PredictModelDto();
        predictModelDto.setModel_name("test_model");
        predictModelDto.setFunc_type("predict");
        predictModelDto.setMax_seq_len(10000);
        predictModelDto.setInput(buildInput());

        List<Map<String, Object>> maps = simplePredictService.predictWithCache(predictModelDto);
        System.out.println("maps... " + maps);
    }

    @Test
    public void test() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method sortResultMap = TestCaseHelper.getPrivateMethodForTest(SimplePredictServiceImpl.class, "sortResultMap", new Class[]{Map.class, List.class});
        Map<String, List<String>> inputMap = new HashMap<>();
        inputMap.put("测试2", Lists.newArrayList("2"));
        inputMap.put("测试1", Lists.newArrayList("1"));
        inputMap.put("测试4", Lists.newArrayList("4"));
        inputMap.put("测试3", Lists.newArrayList("3"));

        List<String> orderInput = Lists.newArrayList("测试4","测试3","测试2","测试1");
        Map<String, List<String>> invoke = (Map<String, List<String>>) sortResultMap.invoke(simplePredictServiceImpl, new Object[]{inputMap, orderInput});
        List<List<String>> collect = invoke.values().stream().collect(Collectors.toList());
        char[] actuals = new char[4];
        for (int i = 0; i < collect.size(); i++) {
            char[] chars = collect.get(i).get(0).toCharArray();
            actuals[i] = chars[0];
        }
        char[] expect = {'4', '3', '2', '1'};
        Assert.assertArrayEquals(null, expect, actuals);
    }
}
