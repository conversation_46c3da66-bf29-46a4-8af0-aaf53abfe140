package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackage;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.AlgoPackageDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoPackagePo;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.util.LocationUtil;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import java.lang.reflect.Field;
import java.util.Map;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

@RunWith(MockitoJUnitRunner.class)
public class AlgoPackageServiceImplTest {

    @Mock
    private AlgoPackageDao algoPackageDao;

    @InjectMocks
    private AlgoPackageServiceImpl algoPackageService;

    @Mock
    private LocationUtilWrapper locationUtilWrapper;

    /**
     * 测试加载算法包时，如果算法包不存在应抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testLoadPackageNotFound() {
        // arrange
        Long id = 1L;
        when(algoPackageDao.getById(id)).thenReturn(null);
        // act
        algoPackageService.loadPackage(id);
        // assert
        // Expected exception
    }
//
//    /**
//     * 测试加载算法包时，如果代码拉取失败应抛出 IllegalStateException
//     */
//    @Test(expected = IllegalStateException.class)
//    public void testLoadPackagePullCodeFail() {
//        // arrange
//        Long id = 1L;
//        AlgoPackagePo po = AlgoPackagePo.builder().id(id).note("Test Note").ownerMis("TestOwner").runtime("Python").modulePath("/path/to/module").version("1.0.0").build();
//        AlgoPackage algoPackage = mock(AlgoPackage.class);
//        when(algoPackageDao.getById(id)).thenReturn(po);
//        when(algoPackage.pullCode()).thenReturn(false);
//        // act
//        algoPackageService.loadPackage(id);
//        // assert
//        // Expected exception
//    }

//    @Before
//    public void setUp() throws Exception {
//        // 使用包装类模拟 LocationUtil.getRuntimePath() 方法的返回值
//        when(locationUtilWrapper.getRuntimePath()).thenReturn("/mocked/path");
//    }

    // 创建一个包装 LocationUtil 静态方法的包装类
    public static class LocationUtilWrapper {

        public String getRuntimePath() {
            return LocationUtil.getRuntimePath();
        }
    }
}
