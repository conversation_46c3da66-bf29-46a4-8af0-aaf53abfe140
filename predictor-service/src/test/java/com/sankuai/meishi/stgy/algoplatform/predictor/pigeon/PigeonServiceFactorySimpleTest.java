package com.sankuai.meishi.stgy.algoplatform.predictor.pigeon;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.dianping.pigeon.remoting.common.service.GenericService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * PigeonServiceFactory 简化单元测试（不使用PowerMock）
 * 主要测试缓存机制和参数处理逻辑
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@RunWith(MockitoJUnitRunner.class)
public class PigeonServiceFactorySimpleTest {

    private PigeonServiceFactory pigeonServiceFactory;
    private GenericService mockGenericService;

    @Before
    public void setUp() {
        pigeonServiceFactory = new PigeonServiceFactory();
        mockGenericService = mock(GenericService.class);
        
        // 清空静态缓存
        PigeonServiceFactory.pigeonGenericService.clear();
    }

    /**
     * 测试从缓存获取代理
     */
    @Test
    public void testCreatePigeonProxy_FromCache() {
        // arrange
        PigeonServiceInfo serviceInfo = createPigeonServiceInfo("com.sankuai.test", "com.test.Service", "testMethod", 5000, "test-cell");
        String cacheKey = "com.sankuai.test_com.test.Service_test-cell_5000";
        
        // 预先放入缓存
        PigeonServiceFactory.pigeonGenericService.put(cacheKey, mockGenericService);

        // act
        GenericService result = pigeonServiceFactory.createPigeonProxy(serviceInfo);

        // assert
        assertNotNull(result);
        assertEquals(mockGenericService, result);
        
        // 验证缓存中仍然存在
        assertTrue(PigeonServiceFactory.pigeonGenericService.containsKey(cacheKey));
        assertEquals(mockGenericService, PigeonServiceFactory.pigeonGenericService.get(cacheKey));
    }

    /**
     * 测试缓存key生成逻辑 - 正常情况
     */
    @Test
    public void testCacheKeyGeneration_Normal() {
        // arrange
        PigeonServiceInfo serviceInfo = createPigeonServiceInfo("com.sankuai.test", "com.test.Service", "testMethod", 5000, "test-cell");
        String expectedKey = "com.sankuai.test_com.test.Service_test-cell_5000";
        
        // 预先放入缓存
        PigeonServiceFactory.pigeonGenericService.put(expectedKey, mockGenericService);

        // act
        GenericService result = pigeonServiceFactory.createPigeonProxy(serviceInfo);

        // assert
        assertNotNull(result);
        assertEquals(mockGenericService, result);
    }

    /**
     * 测试缓存key生成逻辑 - 空Cell
     */
    @Test
    public void testCacheKeyGeneration_EmptyCell() {
        // arrange
        PigeonServiceInfo serviceInfo = createPigeonServiceInfo("com.sankuai.test", "com.test.Service", "testMethod", 5000, "");
        String expectedKey = "com.sankuai.test_com.test.Service__5000";
        
        // 预先放入缓存
        PigeonServiceFactory.pigeonGenericService.put(expectedKey, mockGenericService);

        // act
        GenericService result = pigeonServiceFactory.createPigeonProxy(serviceInfo);

        // assert
        assertNotNull(result);
        assertEquals(mockGenericService, result);
    }

    /**
     * 测试缓存key生成逻辑 - null Cell
     */
    @Test
    public void testCacheKeyGeneration_NullCell() {
        // arrange
        PigeonServiceInfo serviceInfo = createPigeonServiceInfo("com.sankuai.test", "com.test.Service", "testMethod", 5000, null);
        String expectedKey = "com.sankuai.test_com.test.Service_null_5000";
        
        // 预先放入缓存
        PigeonServiceFactory.pigeonGenericService.put(expectedKey, mockGenericService);

        // act
        GenericService result = pigeonServiceFactory.createPigeonProxy(serviceInfo);

        // assert
        assertNotNull(result);
        assertEquals(mockGenericService, result);
    }

    /**
     * 测试多个不同服务的缓存
     */
    @Test
    public void testMultipleServiceCaching() {
        // arrange
        PigeonServiceInfo serviceInfo1 = createPigeonServiceInfo("com.sankuai.test1", "com.test.Service1", "testMethod1", 5000, "cell1");
        PigeonServiceInfo serviceInfo2 = createPigeonServiceInfo("com.sankuai.test2", "com.test.Service2", "testMethod2", 6000, "cell2");
        
        GenericService mockService1 = mock(GenericService.class);
        GenericService mockService2 = mock(GenericService.class);
        
        String key1 = "com.sankuai.test1_com.test.Service1_cell1_5000";
        String key2 = "com.sankuai.test2_com.test.Service2_cell2_6000";
        
        // 预先放入缓存
        PigeonServiceFactory.pigeonGenericService.put(key1, mockService1);
        PigeonServiceFactory.pigeonGenericService.put(key2, mockService2);

        // act
        GenericService result1 = pigeonServiceFactory.createPigeonProxy(serviceInfo1);
        GenericService result2 = pigeonServiceFactory.createPigeonProxy(serviceInfo2);

        // assert
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotEquals(result1, result2);
        assertEquals(mockService1, result1);
        assertEquals(mockService2, result2);
        
        // 验证缓存
        assertEquals(2, PigeonServiceFactory.pigeonGenericService.size());
        assertTrue(PigeonServiceFactory.pigeonGenericService.containsKey(key1));
        assertTrue(PigeonServiceFactory.pigeonGenericService.containsKey(key2));
    }

    /**
     * 测试缓存清空功能
     */
    @Test
    public void testCacheClear() {
        // arrange
        String cacheKey = "com.sankuai.test_com.test.Service_test-cell_5000";
        
        // 预先放入缓存
        PigeonServiceFactory.pigeonGenericService.put(cacheKey, mockGenericService);
        assertEquals(1, PigeonServiceFactory.pigeonGenericService.size());

        // act
        PigeonServiceFactory.pigeonGenericService.clear();

        // assert
        assertEquals(0, PigeonServiceFactory.pigeonGenericService.size());
        assertFalse(PigeonServiceFactory.pigeonGenericService.containsKey(cacheKey));
    }

    /**
     * 测试缓存容量
     */
    @Test
    public void testCacheCapacity() {
        // arrange & act
        for (int i = 0; i < 10; i++) {
            PigeonServiceInfo serviceInfo = createPigeonServiceInfo("com.sankuai.test" + i, "com.test.Service" + i, "testMethod", 5000, "cell" + i);
            String cacheKey = "com.sankuai.test" + i + "_com.test.Service" + i + "_cell" + i + "_5000";
            GenericService mockService = mock(GenericService.class);
            
            PigeonServiceFactory.pigeonGenericService.put(cacheKey, mockService);
            
            GenericService result = pigeonServiceFactory.createPigeonProxy(serviceInfo);
            assertNotNull(result);
            assertEquals(mockService, result);
        }

        // assert
        assertEquals(10, PigeonServiceFactory.pigeonGenericService.size());
    }

    /**
     * 测试相同服务信息获取相同代理
     */
    @Test
    public void testSameServiceInfoGetsSameProxy() {
        // arrange
        PigeonServiceInfo serviceInfo1 = createPigeonServiceInfo("com.sankuai.test", "com.test.Service", "testMethod", 5000, "test-cell");
        PigeonServiceInfo serviceInfo2 = createPigeonServiceInfo("com.sankuai.test", "com.test.Service", "testMethod", 5000, "test-cell");
        String cacheKey = "com.sankuai.test_com.test.Service_test-cell_5000";
        
        // 预先放入缓存
        PigeonServiceFactory.pigeonGenericService.put(cacheKey, mockGenericService);

        // act
        GenericService result1 = pigeonServiceFactory.createPigeonProxy(serviceInfo1);
        GenericService result2 = pigeonServiceFactory.createPigeonProxy(serviceInfo2);

        // assert
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(result1, result2);
        assertEquals(mockGenericService, result1);
        assertEquals(mockGenericService, result2);
    }

    /**
     * 测试不同超时时间生成不同缓存key
     */
    @Test
    public void testDifferentTimeoutGeneratesDifferentKey() {
        // arrange
        PigeonServiceInfo serviceInfo1 = createPigeonServiceInfo("com.sankuai.test", "com.test.Service", "testMethod", 5000, "test-cell");
        PigeonServiceInfo serviceInfo2 = createPigeonServiceInfo("com.sankuai.test", "com.test.Service", "testMethod", 6000, "test-cell");
        
        GenericService mockService1 = mock(GenericService.class);
        GenericService mockService2 = mock(GenericService.class);
        
        String key1 = "com.sankuai.test_com.test.Service_test-cell_5000";
        String key2 = "com.sankuai.test_com.test.Service_test-cell_6000";
        
        // 预先放入缓存
        PigeonServiceFactory.pigeonGenericService.put(key1, mockService1);
        PigeonServiceFactory.pigeonGenericService.put(key2, mockService2);

        // act
        GenericService result1 = pigeonServiceFactory.createPigeonProxy(serviceInfo1);
        GenericService result2 = pigeonServiceFactory.createPigeonProxy(serviceInfo2);

        // assert
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotEquals(result1, result2);
        assertEquals(mockService1, result1);
        assertEquals(mockService2, result2);
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建PigeonServiceInfo测试对象
     */
    private PigeonServiceInfo createPigeonServiceInfo(String appKey, String interfaceName, String methodName, int timeout, String cell) {
        PigeonServiceInfo serviceInfo = new PigeonServiceInfo();
        serviceInfo.setAppKey(appKey);
        serviceInfo.setInterfaceName(interfaceName);
        serviceInfo.setMethodName(methodName);
        serviceInfo.setTimeout(timeout);
        serviceInfo.setCell(cell);
        return serviceInfo;
    }
}
