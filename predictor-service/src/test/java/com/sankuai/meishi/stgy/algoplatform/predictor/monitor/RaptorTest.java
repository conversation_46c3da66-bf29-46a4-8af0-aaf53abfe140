package com.sankuai.meishi.stgy.algoplatform.predictor.monitor;

import com.google.common.collect.ImmutableMap;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack.*;

@RunWith(MockitoJUnitRunner.class)
public class RaptorTest {

    @Test
    public void testCat() {
        Map<String, String> map = new HashMap<String, String>() {
            {
                put("mtSecondCate", "烧烤");
                put("bizCode", "test");
            }
        };

        // 一级缓存打点测试 bizCode useCache cate
        PREDICT_CACHE_NUM.report("test", "true", map);
        PREDICT_CACHE_NUM.report("test", "true", null);
        try {
            PREDICT_CACHE_NUM.report("test", "true");
        } catch (Exception e) {
            assert e.getClass() == ArrayIndexOutOfBoundsException.class;
        }


        // 二级缓存打点测试 modelName useCache cate
        MODEL_GLOBAL_CACHE_NUM.report("bml_remark_distinct_model", "true", map);
        MODEL_GLOBAL_CACHE_NUM.report("bml_remark_distinct_model", "true", null);
        try {
            MODEL_GLOBAL_CACHE_NUM.report("bml_remark_distinct_model", "true");
        } catch (Exception e) {
            assert e.getClass() == ArrayIndexOutOfBoundsException.class;
        }

        //三级缓存打点测试 modelName useCache cnt cate
        MODEL_CACHE_NUM.report("bml_remark_distinct_model", "false", 1, map);
        MODEL_CACHE_NUM.report("bml_remark_distinct_model", "false", 1, null);
        try {
            MODEL_CACHE_NUM.report("bml_remark_distinct_model", "true");
        } catch (Exception e) {
            assert e.getClass() == ArrayIndexOutOfBoundsException.class;
        }

        //四级缓存打点测试 modelName useCache cnt cate
        MODEL_4LEVEL_SECOND_CACHE_NUM.report("bml_remark_distinct_model", "false", 1, map);
        MODEL_4LEVEL_SECOND_CACHE_NUM.report("bml_remark_distinct_model", "false", 1, null);
        try {
            MODEL_4LEVEL_SECOND_CACHE_NUM.report("bml_remark_distinct_model", "true");
        } catch (Exception e) {
            assert e.getClass() == ArrayIndexOutOfBoundsException.class;
        }

    }
}
