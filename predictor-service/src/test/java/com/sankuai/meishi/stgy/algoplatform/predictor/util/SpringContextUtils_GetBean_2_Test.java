package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.mockito.InjectMocks.*;

@RunWith(MockitoJUnitRunner.class)
public class SpringContextUtils_GetBean_2_Test {

    @Mock
    private ApplicationContext mockApplicationContext;

    @Before
    public void setUp() {
        // 通过反射设置 applicationContext 为 mock 对象
        ReflectionTestUtils.setField(SpringContextUtils.class, "applicationContext", mockApplicationContext);
    }

    /**
     * 测试 getBean 方法在 applicationContext 非空且能找到 bean 的情况
     */
    @Test
    public void testGetBean_Normal() throws Throwable {
        // arrange
        String beanName = "testBean";
        Object beanInstance = new Object();
        when(mockApplicationContext.getBean(beanName, Object.class)).thenReturn(beanInstance);
        // act
        Object result = SpringContextUtils.getBean(beanName, Object.class);
        // assert
        assertNotNull(result);
        assertEquals(beanInstance, result);
    }

    /**
     * 测试 getBean 方法在 applicationContext 为 null 的情况
     */
    @Test(expected = NullPointerException.class)
    public void testGetBean_ApplicationContextIsNull() throws Throwable {
        // arrange
        ReflectionTestUtils.setField(SpringContextUtils.class, "applicationContext", null);
        String beanName = "testBean";
        // act
        SpringContextUtils.getBean(beanName, Object.class);
        // assert
        // 预期抛出 NullPointerException
    }

    /**
     * 测试 getBean 方法在 applicationContext 非空但找不到 bean 的情况
     */
    @Test(expected = BeansException.class)
    public void testGetBean_BeanNotFound() throws Throwable {
        // arrange
        String beanName = "nonExistentBean";
        when(mockApplicationContext.getBean(beanName, Object.class)).thenThrow(new BeansException("Bean not found") {
        });
        // act
        SpringContextUtils.getBean(beanName, Object.class);
        // assert
        // 预期抛出 BeansException
    }
}
