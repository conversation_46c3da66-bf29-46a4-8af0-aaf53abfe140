package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import java.lang.reflect.Method;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.InjectMocks.*;

/**
 * SpringContextUtils 类的单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class SpringContextUtils_GetBeanTest {

    @Mock
    private ApplicationContext applicationContextMock;

    private SpringContextUtils springContextUtils;

    @Before
    public void setUp() throws Exception {
        springContextUtils = new SpringContextUtils();
        Method setApplicationContextMethod = SpringContextUtils.class.getDeclaredMethod("setApplicationContext", ApplicationContext.class);
        setApplicationContextMethod.setAccessible(true);
        setApplicationContextMethod.invoke(springContextUtils, applicationContextMock);
    }

    /**
     * 测试 getBean 方法在正常情况下的行为
     */
    @Test
    public void testGetBeanNormal() throws BeansException {
        // arrange
        String beanName = "testBean";
        Object expectedBean = new Object();
        when(applicationContextMock.getBean(beanName)).thenReturn(expectedBean);
        // act
        Object actualBean = SpringContextUtils.getBean(beanName);
        // assert
        assertNotNull("应该获取到 bean 实例", actualBean);
    }

    /**
     * 测试 getBean 方法在 applicationContext 为 null 时的行为
     */
    @Test(expected = IllegalStateException.class)
    public void testGetBeanApplicationContextIsNull() throws Exception {
        // arrange
        when(applicationContextMock.getBean(anyString())).thenThrow(new IllegalStateException());
        String beanName = "testBean";
        // act
        Object actualBean = SpringContextUtils.getBean(beanName);
        // assert
        // 预期抛出 IllegalStateException
    }

    /**
     * 测试 getBean 方法在 bean 不存在时的行为
     */
    @Test(expected = BeansException.class)
    public void testGetBeanBeanNotFound() throws BeansException {
        // arrange
        String beanName = "nonExistentBean";
        when(applicationContextMock.getBean(beanName)).thenThrow(new NoSuchBeanDefinitionException(beanName));
        // act
        Object actualBean = SpringContextUtils.getBean(beanName);
        // assert
        // 预期抛出 BeansException
    }
}
