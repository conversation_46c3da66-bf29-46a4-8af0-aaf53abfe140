package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import com.sankuai.meishi.stgy.algoplatform.predictor.service.TairClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/9/7 16:09
 **/
//@RunWith(SpringRunner.class)
//@SpringBootTest
@Slf4j
public class SpringPredictTest {

//    @Autowired
    private TairClient tairTestClient;

//    @Test
    public void testTairPut(){

        String key="zb_dealmatching_1_11833b158cbe7a18526aa1ae98790c50";
        boolean put = tairTestClient.put(key, "1", 1L, TimeUnit.DAYS);
        Assert.assertEquals(true,put);
    }

//    @Test
    public void testGetTair(){

        String s = tairTestClient.get("zb_dealmatching_1_11833b158cbe7a18526aa1ae98790c50");

        System.out.println(s);
    }

}
