package com.sankuai.meishi.stgy.algoplatform.predictor.service;

import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.taobao.tair3.client.Result;
import com.taobao.tair3.client.impl.MultiTairClient;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ MdpContextUtils.class })
public class TairClient_GetTest {

    @Mock
    private MultiTairClient mockTairClient;

    @InjectMocks
    private TairClient tairClientUnderTest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockStatic(MdpContextUtils.class);
        when(MdpContextUtils.getHostEnv()).thenReturn(HostEnv.PROD);
    }

    @Test
    public void testGetWhenHostEnvIsProd() throws Exception {
        // arrange
        String key = "testKey";
        String expectedValue = "testValue";
        Result<byte[]> mockResult = mock(Result.class);
        when(mockResult.isOK()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(expectedValue.getBytes());
        when(mockTairClient.get(eq((short) 15), eq(key.getBytes()), any())).thenReturn(mockResult);
        // act
        String result = tairClientUnderTest.get(key);
        // assert
        assertEquals(expectedValue, result);
    }

    @Test
    public void testGetWhenTairReturnsNull() throws Exception {
        // arrange
        String key = "testKey";
        Result<byte[]> mockResult = mock(Result.class);
        when(mockResult.isOK()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(null);
        when(mockTairClient.get(anyShort(), eq(key.getBytes()), eq(tairClientUnderTest.TAIR_OPTION))).thenReturn(mockResult);
        // act
        String result = tairClientUnderTest.get(key);
        // assert
        assertNull(result);
    }

    // ... 其他测试用例保持不变 ...
    @Test
    public void testGetWhenTairReturnsError() throws Exception {
        // arrange
        String key = "testKey";
        Result<byte[]> mockResult = mock(Result.class);
        when(mockResult.isOK()).thenReturn(false);
        when(mockTairClient.get(anyShort(), eq(key.getBytes()), eq(tairClientUnderTest.TAIR_OPTION))).thenReturn(mockResult);
        // act
        String result = tairClientUnderTest.get(key);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetWhenTairThrowsException() throws Exception {
        // arrange
        String key = "testKey";
        when(mockTairClient.get(anyShort(), eq(key.getBytes()), eq(tairClientUnderTest.TAIR_OPTION))).thenThrow(new RuntimeException("Tair exception"));
        // act
        String result = tairClientUnderTest.get(key);
        // assert
        assertNull(result);
    }
}
