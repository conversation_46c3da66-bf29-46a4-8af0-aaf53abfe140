package com.sankuai.meishi.stgy.algoplatform.predictor;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2024年05月14日 8:05 下午
 */
public class TestCaseHelper {
    /**
     * 测试私有方法时使用，将类的私有方法转换成可访问的方法。
     * @param clazz 类的类型，不能为空
     * @param methodName 方法名称，不能为空
     * @param argClasses 方法的参数类数组，不能为空，也不能没有元素
     * @return 可访问的方法对象
     */
    public static Method getPrivateMethodForTest(Class<?> clazz,
                                                 String methodName, Class<?>[] argClasses)
            throws NoSuchMethodException, SecurityException {
        if (clazz == null || methodName == null || argClasses == null) {
            throw new IllegalArgumentException("clazz、methodName和argClasses都不能为空。");
        }
        // 获取私有方法和他的参数
        Method method = clazz.getDeclaredMethod(methodName, argClasses);
        method.setAccessible(true); // 允许访问
        return method;
    }

    /**
     * 获取私有类属性的值
     *
     * @param clazz
     *            类的类型，不能为空
     * @param obj
     *            类的实例，不能为空
     * @param fieldName
     *            类属性名称，不能为空
     * @return 类属性的值
     * @throws NoSuchFieldException
     *             类属性名称不存在时抛出
     * @throws SecurityException
     * @throws IllegalArgumentException
     *             三个参数中任一参数为空时抛出
     * @throws IllegalAccessException
     */
    public static Object getPrivateFieldValue(Class<?> clazz, Object obj,
                                              String fieldName) throws NoSuchFieldException, SecurityException,
            IllegalArgumentException, IllegalAccessException {
        if (clazz == null || obj == null || fieldName == null) {
            throw new IllegalArgumentException("clazz、obj和fieldName都不能为空");
        }
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    /**
     * 设置私有类属性的值
     *
     * @param clazz
     *            类的类型，不能为空
     * @param obj
     *            类的实例，不能为空
     * @param fieldName
     *            类属性名称，不能为空
     * @param value
     *            类属性的值
     * @throws NoSuchFieldException
     *             类属性名称不存在时抛出
     * @throws SecurityException
     * @throws IllegalArgumentException
     *             三个参数中任一参数为空时抛出
     * @throws IllegalAccessException
     */
    public static void setPrivateFieldValue(Class<?> clazz, Object obj,
                                            String fieldName, Object value) throws NoSuchFieldException,
            SecurityException, IllegalArgumentException, IllegalAccessException {
        if (clazz == null || obj == null || fieldName == null) {
            throw new IllegalArgumentException("clazz、obj和fieldName都不能为空");
        }
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(obj, value);
    }
}