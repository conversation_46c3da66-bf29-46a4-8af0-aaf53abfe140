package com.sankuai.meishi.stgy.algoplatform.predictor.service.impl;

import com.google.common.cache.LoadingCache;
import com.google.protobuf.ByteString;
import com.meituan.hadoop.afo.serving.common.OnlineService;
import com.meituan.hadoop.afo.triton.InferenceServerClient;
import inference.GrpcService;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

/**
 * 测试 SimplePredictServiceImpl 的 tritonPredict 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class SimplePredictServiceImpl_TritonPredictTest {

    @Mock
    private InferenceServerClient mockInferenceServerClient;

    @Mock
    private OnlineService.Iface mockAfoClient;

    @Mock
    private LoadingCache<String, InferenceServerClient> mockTritonPredictClientCache;

    private SimplePredictServiceImpl simplePredictService;

    @Before
    public void setUp() throws ExecutionException {
        simplePredictService = new SimplePredictServiceImpl();
        setPrivateField(simplePredictService, "afoClient", mockAfoClient);
        setPrivateField(simplePredictService, "tritonPredictClientCache", mockTritonPredictClientCache);
        when(mockTritonPredictClientCache.get(any())).thenReturn(mockInferenceServerClient);
    }

    /**
     * 测试 tritonPredict 方法正常情况
     */
    @Test
    public void testTritonPredictNormal() throws Throwable {
        // arrange
        String modelName = "testModel";
        String modelVersion = "1";
        LinkedHashMap<String, List<Object>> inputValue = new LinkedHashMap<>();
        Map<String, List<Integer>> inputShape = new LinkedHashMap<>();
        Map<String, String> inputType = new LinkedHashMap<>();
        LinkedHashMap<String, String> outputType = new LinkedHashMap<>();
        GrpcService.ModelInferResponse mockResponse = GrpcService.ModelInferResponse.newBuilder().addRawOutputContents(ByteString.copyFromUtf8("testOutput")).build();
        when(mockInferenceServerClient.predict(any())).thenReturn(mockResponse);
        // act
        LinkedHashMap<String, List<Object>> result = simplePredictService.tritonPredict(modelName, modelVersion, inputValue, inputShape, inputType, outputType);
        // assert
        assertNotNull(result);
        verify(mockInferenceServerClient, times(1)).predict(any());
    }

    /**
     * 测试 tritonPredict 方法当 getTritonClient 抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testTritonPredictGetTritonClientThrowsException() throws Throwable {
        // arrange
        when(mockTritonPredictClientCache.get(any())).thenThrow(new ExecutionException(new Exception("Test Exception")));
        // act
        simplePredictService.tritonPredict("testModel", "1", new LinkedHashMap<>(), new LinkedHashMap<>(), new LinkedHashMap<>(), new LinkedHashMap<>());
        // assert is handled by the expected exception
    }

    /**
     * 测试 tritonPredict 方法当 predict 抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testTritonPredictPredictThrowsException() throws Throwable {
        // arrange
        when(mockInferenceServerClient.predict(any())).thenThrow(new Exception("Test Exception"));
        // act
        simplePredictService.tritonPredict("testModel", "1", new LinkedHashMap<>(), new LinkedHashMap<>(), new LinkedHashMap<>(), new LinkedHashMap<>());
        // assert is handled by the expected exception
    }

    // 反射设置私有属性的辅助方法
    private void setPrivateField(Object target, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(target, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
