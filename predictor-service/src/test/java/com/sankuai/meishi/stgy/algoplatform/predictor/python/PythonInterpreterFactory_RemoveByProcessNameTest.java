package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.*;

public class PythonInterpreterFactory_RemoveByProcessNameTest {

    private void setRuntimes(Map<String, List<PythonInterpreter>> runtimes) throws Exception {
        Field field = PythonInterpreterFactory.class.getDeclaredField("runtimes");
        field.setAccessible(true);
        // 移除 final 修饰符
        Field modifiersField = Field.class.getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(field, field.getModifiers() & ~Modifier.FINAL);
        field.set(null, runtimes);
    }

    private Map<String, List<PythonInterpreter>> getRuntimes() throws Exception {
        Field field = PythonInterpreterFactory.class.getDeclaredField("runtimes");
        field.setAccessible(true);
        return (Map<String, List<PythonInterpreter>>) field.get(null);
    }

    /**
     * 测试removeByProcessName方法，当传入的PythonInterpreter在runtimes中不存在时，不应该有任何变化。
     */
    @Test
    public void testRemoveByProcessName_InterpreterNotExists() throws Exception {
        // arrange
        String name = "testInterpreter";
        String processName = "process1";
        List<String> features = new ArrayList<>();
        PythonInterpreter interpreter = new PythonInterpreter(name, processName, features);
        PythonInterpreter anotherInterpreter = new PythonInterpreter(name, "process2", features);
        List<PythonInterpreter> interpreters = new ArrayList<>();
        interpreters.add(anotherInterpreter);
//        setRuntimes(new HashMap<String, List<PythonInterpreter>>() {
//
//            {
//                put(name, interpreters);
//            }
//        });
        // act
        PythonInterpreterFactory.removeByProcessName(interpreter);
        // assert
        Assert.assertNull(getRuntimes().get(name));

        // 重置静态字段以避免测试间的干扰
//        setRuntimes(new HashMap<>());
    }

    /**
     * 测试removeByProcessName方法，当runtimes为空时，不应该有任何变化。
     */
    @Test
    public void testRemoveByProcessName_EmptyRuntimes() throws Exception {
        // arrange
        String name = "testInterpreter";
        String processName = "process1";
        List<String> features = new ArrayList<>();
        PythonInterpreter interpreter = new PythonInterpreter(name, processName, features);
        // act
        PythonInterpreterFactory.removeByProcessName(interpreter);
        // assert
        Assert.assertTrue("Runtimes should remain empty", getRuntimes().isEmpty());
    }
}
