package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import org.junit.Test;
import static org.junit.Assert.*;

public class MD5Util_MainTest {

    /**
     * 测试 md5 方法，输入正常字符串
     */
    @Test
    public void testMd5NormalInput() {
        // arrange
        String input = "[\"手擀面\",\"手工面\"]";
        // 替换为实际的MD5值
        String expected = "cdf380b63615536044df988ba368b1ef";
        // act
        String actual = MD5Util.md5(input);
        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试 md5 方法，输入为 null
     */
    @Test
    public void testMd5NullInput() {
        // arrange
        String input = null;
        // act
        String actual = MD5Util.md5(input);
        // assert
        assertNull(actual);
    }

    /**
     * 测试 md5 方法，输入为空字符串
     */
    @Test
    public void testMd5EmptyStringInput() {
        // arrange
        String input = "";
        // 空字符串的MD5值
        String expected = "d41d8cd98f00b204e9800998ecf8427e";
        // act
        String actual = MD5Util.md5(input);
        // assert
        assertEquals(expected, actual);
    }
}
