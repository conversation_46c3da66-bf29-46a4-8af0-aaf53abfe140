package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import org.junit.*;
import org.junit.rules.ExpectedException;
import py4j.GatewayServer;

public class PythonInterpreterTest {

    private PythonInterpreter interpreter;

    private Process mockProcess;

    private PythonInterpreter pythonInterpreter = new PythonInterpreter("testName", "testProcessName", new ArrayList<>());

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @After
    public void resetStaticFields() throws Exception {
        // 使用反射机制重置静态字段runtimePath的值
        try {
            Field field = Class.forName("com.sankuai.meishi.stgy.algoplatform.predictor.python.util.LocationUtil").getDeclaredField("runtimePath");
            field.setAccessible(true);
            field.set(null, null);
        } catch (NoSuchFieldException e) {
            // 忽略异常，因为可能在不同的测试环境中，该字段不存在或已被重命名
        }
    }

    @After
    public void resetPortToDefault() {
        // 重置端口号为默认值，避免静态字段修改影响其他测试
        pythonInterpreter.setPort(GatewayServer.DEFAULT_PORT);
    }

    /**
     * 设置测试环境，模拟PythonInterpreter实例和Process实例
     */
    private void setupTestEnvironment(boolean processIsNull) throws Exception {
        List<String> features = new ArrayList<>();
        interpreter = new PythonInterpreter("testName", "testProcess", features);
        if (!processIsNull) {
            mockProcess = mock(Process.class);
            Field pythonProcessField = PythonInterpreter.class.getDeclaredField("pythonProcess");
            pythonProcessField.setAccessible(true);
            pythonProcessField.set(interpreter, mockProcess);
        }
    }

    /**
     * 使用反射设置对象内部状态。
     */
    private void setInternalState(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    /**
     * 使用反射设置私有字段的值
     *
     * @param object    目标对象
     * @param fieldName 字段名
     * @param value     设置的值
     */
    private void setPrivateField(Object object, String fieldName, Object value) {
        try {
            Field field = object.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(object, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试restart方法，当IOException被抛出时
     */
    @Test
    public void testRestartWhenIOExceptionIsThrown() throws Throwable {
        // 在这里模拟LocationUtil.getRuntimePath()的返回值，避免抛出异常
        System.setProperty("runtimePath", "/fake/path");
        List<String> features = new ArrayList<>();
        PythonInterpreter interpreter = new PythonInterpreter("testName", "testProcess", features);
        interpreter.restart();
        Assert.assertTrue(interpreter.getPort() == 25333);
    }

    /**
     * 测试destroy方法，当pythonProcess不为null时
     */
    @Test
    public void testDestroyWhenPythonProcessIsNotNull() throws Exception {
        // arrange
        List<String> features = new ArrayList<>();
        PythonInterpreter interpreter = new PythonInterpreter("testName", "testProcess", features);
        Process mockProcess = mock(Process.class);
        Field pythonProcessField = PythonInterpreter.class.getDeclaredField("pythonProcess");
        pythonProcessField.setAccessible(true);
        pythonProcessField.set(interpreter, mockProcess);
        // act
        interpreter.destroy();
        // assert
        verify(mockProcess, times(1)).destroy();
    }

    /**
     * 测试destroy方法，当pythonProcess为null时
     */
    @Test
    public void testDestroyWhenPythonProcessIsNull() throws Exception {
        // arrange
        List<String> features = new ArrayList<>();
        PythonInterpreter interpreter = new PythonInterpreter("testName", "testProcess", features);
        // act
        interpreter.destroy();
        // assert
        Assert.assertNull(interpreter.getPythonProcess());
    }

    /**
     * 测试getPrecessId方法，当pythonProcess为null时应返回null。
     */
    @Test
    public void testGetPrecessIdWhenPythonProcessIsNull() throws Throwable {
        // arrange
        List<String> features = new ArrayList<>();
        PythonInterpreter interpreter = new PythonInterpreter("testName", "testProcessName", features);
        // act
        String result = interpreter.getPrecessId();
        // assert
        assertNull("预期返回null，因为pythonProcess是null", result);
    }

    /**
     * 测试getPrecessId方法，当获取进程ID时发生异常。
     */
    @Test
    public void testGetPrecessIdWhenExceptionOccurs() throws Throwable {
        // arrange
        List<String> features = new ArrayList<>();
        PythonInterpreter interpreter = new PythonInterpreter("testName", "testProcessName", features);
        Process mockProcess = mock(Process.class);
        setInternalState(interpreter, "pythonProcess", mockProcess);
        // 由于无法直接模拟异常，这里不设置pid，让其自然抛出异常
        // act
        String result = interpreter.getPrecessId();
        // assert
        assertNull("预期返回null，因为获取进程ID时发生异常", result);
    }

    /**
     * 心跳成功测试
     */
    @Test
    public void testHeartbeatSuccess() {
        // arrange
        GatewayServer gatewayServer = mock(GatewayServer.class);
        PythonBridge pythonBridge = mock(PythonBridge.class);
        List<String> features = new ArrayList<>();
        when(gatewayServer.getPythonServerEntryPoint(new Class[]{PythonBridge.class})).thenReturn(pythonBridge);
        when(pythonBridge.say_hello("processName")).thenReturn("hello:processName");
        PythonInterpreter pythonInterpreter = new PythonInterpreter("name", "processName", features);
        setPrivateField(pythonInterpreter, "gatewayServer", gatewayServer);
        // act
        boolean result = pythonInterpreter.heartbeat();
        // assert
        assertTrue(result);
    }

    /**
     * 心跳异常测试
     */
    @Test
    public void testHeartbeatException() {
        // arrange
        List<String> features = new ArrayList<>();
        GatewayServer gatewayServer = mock(GatewayServer.class);
        when(gatewayServer.getPythonServerEntryPoint(new Class[]{PythonBridge.class})).thenThrow(new RuntimeException("Mock Exception"));
        PythonInterpreter pythonInterpreter = new PythonInterpreter("name", "processName", features);
        setPrivateField(pythonInterpreter, "gatewayServer", gatewayServer);
        // act
        boolean result = pythonInterpreter.heartbeat();
        // assert
        assertFalse(result);
    }

    /**
     * 测试setPort方法，设置一个有效的端口号
     */
    @Test
    public void testSetPortValidPort() {
        // arrange
        int validPort = 25500;
        // act
        pythonInterpreter.setPort(validPort);
        // assert
        Assert.assertEquals("设置有效端口号失败", validPort, pythonInterpreter.getPort());
    }
}
