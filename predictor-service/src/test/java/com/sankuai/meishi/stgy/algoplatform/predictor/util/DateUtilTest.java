package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import org.junit.Test;
import static org.junit.Assert.*;
import java.util.Date;
import java.text.ParseException;

public class DateUtilTest {

    /**
     * 测试正常情况下的日期格式化
     */
    @Test
    public void testFormatDateNormal() throws Throwable {
        // arrange
        Date date = new Date(0);
        String expected = "1970-01-01 08:00:00";
        // act
        String result = DateUtil.formatDate(date, DateUtil.YYYY_MM_DD_HH_MM_SS_FORMAT);
        // assert
        assertEquals(expected, result);
    }

    /**
     * 测试当日期对象为null时的情况
     */
    @Test(expected = NullPointerException.class)
    public void testFormatDateWithNullDate() throws Throwable {
        // arrange
        Date date = null;
        // act
        DateUtil.formatDate(date, DateUtil.YYYY_MM_DD_HH_MM_SS_FORMAT);
        // assert is handled by the expected exception
    }

    /**
     * 测试当格式化字符串为null时的情况
     */
    @Test(expected = NullPointerException.class)
    public void testFormatDateWithNullFormat() throws Throwable {
        // arrange
        Date date = new Date();
        // act
        DateUtil.formatDate(date, null);
        // assert is handled by the expected exception
    }

    /**
     * 测试当格式化字符串为空时的情况
     */
    @Test
    public void testFormatDateWithEmptyFormat() throws Throwable {
        // arrange
        Date date = new Date();
        // act
        String result = DateUtil.formatDate(date, "");
        // assert
        // 确保返回的字符串不为null
        assertNotNull(result);
    }

    /**
     * 测试当格式化字符串为无效格式时的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testFormatDateWithInvalidFormat() throws Throwable {
        // arrange
        Date date = new Date();
        // act
        DateUtil.formatDate(date, "invalid-format");
        // assert is handled by the expected exception
    }
}
