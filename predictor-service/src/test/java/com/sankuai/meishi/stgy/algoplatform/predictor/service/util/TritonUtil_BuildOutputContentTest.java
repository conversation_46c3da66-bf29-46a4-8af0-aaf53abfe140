package com.sankuai.meishi.stgy.algoplatform.predictor.service.util;

import com.google.protobuf.ByteString;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.LongStream;
import java.util.stream.DoubleStream;
import static org.junit.Assert.*;
import org.mockito.Mockito;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.LongBuffer;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

/**
 * TritonUtil.buildOutputContent 方法的单元测试
 */
public class TritonUtil_BuildOutputContentTest {

    private LinkedHashMap<String, String> outputType;

    private List<ByteString> output;

    @Before
    public void setUp() {
        outputType = new LinkedHashMap<>();
        output = new ArrayList<>();
    }

    // Helper method to convert primitive array to list of objects
    private static <T> List<T> toList(T[] array) {
        return new ArrayList<>(Arrays.asList(array));
    }

    // Helper methods to convert primitive arrays to lists of wrapper objects
    private static List<Integer> toList(int[] array) {
        return IntStream.of(array).boxed().collect(Collectors.toList());
    }

    private static List<Long> toList(long[] array) {
        return LongStream.of(array).boxed().collect(Collectors.toList());
    }

    private static List<Float> toList(float[] array) {
        return IntStream.range(0, array.length).mapToObj(i -> array[i]).collect(Collectors.toList());
    }

    private static List<Double> toList(double[] array) {
        return DoubleStream.of(array).boxed().collect(Collectors.toList());
    }

    @Test
    public void testBuildOutputContentStringType() {
        // arrange
        outputType.put("stringKey", "string");

        // 4位 + 字符串长度
        ByteBuffer buffer = ByteBuffer.allocate(4 + 10);
        buffer.order(ByteOrder.LITTLE_ENDIAN);  // 设置为小端字节序读写长度字段
        buffer.putInt(10).put("testString".getBytes(StandardCharsets.UTF_8));
        ByteString byteString = ByteString.copyFrom(buffer.array());
        output.add(byteString);
        // act
        LinkedHashMap<String, List<Object>> result = TritonUtil.buildOutputContent(outputType, output);
        // assert
        assertNotNull(result);
        assertTrue(result.containsKey("stringKey"));
        assertEquals(Arrays.asList("testString"), result.get("stringKey"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBuildOutputContentUnknownType() {
        // arrange
        outputType.put("unknownKey", "unknown");
        output.add(ByteString.EMPTY);
        // act
        TritonUtil.buildOutputContent(outputType, output);
        // assert is handled by the expected exception
    }

    @Test
    public void testBuildOutputContentEmptyOutputType() {
        // arrange
        // outputType is already empty
        // act
        LinkedHashMap<String, List<Object>> result = TritonUtil.buildOutputContent(outputType, new ArrayList<>());
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
