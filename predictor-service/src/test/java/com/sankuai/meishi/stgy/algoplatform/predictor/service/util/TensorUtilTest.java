package com.sankuai.meishi.stgy.algoplatform.predictor.service.util;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;
import org.tensorflow.framework.DataType;
import org.tensorflow.framework.TensorProto;
import org.tensorflow.framework.TensorShapeProto;

/**
 * 测试 TensorUtil.buildLongTensor 方法
 */
public class TensorUtilTest {

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    // ... 其他测试方法保持不变 ...
    /**
     * 测试正常情况下构建长整型张量
     */
    @Test
    public void testBuildLongTensorNormal() {
        // arrange
        List<Long> list = Arrays.asList(1L, 2L, 3L, 4L);
        int dim1 = 2;
        int dim2 = 2;
        // act
        TensorProto tensorProto = TensorUtil.buildLongTensor(list, dim1, dim2);
        // assert
        Assert.assertNotNull(tensorProto);
        Assert.assertEquals(tensorProto.getDtype(), org.tensorflow.framework.DataType.DT_INT64);
        Assert.assertEquals(tensorProto.getInt64ValList(), list);
        Assert.assertEquals(tensorProto.getTensorShape().getDimCount(), 2);
        Assert.assertEquals(tensorProto.getTensorShape().getDim(0).getSize(), dim1);
        Assert.assertEquals(tensorProto.getTensorShape().getDim(1).getSize(), dim2);
    }

    /**
     * 测试传入空列表构建长整型张量
     */
    @Test
    public void testBuildLongTensorWithEmptyList() {
        // arrange
        List<Long> list = Collections.emptyList();
        int dim1 = 0;
        int dim2 = 0;
        // act
        TensorProto tensorProto = TensorUtil.buildLongTensor(list, dim1, dim2);
        // assert
        Assert.assertNotNull(tensorProto);
        Assert.assertTrue(tensorProto.getInt64ValList().isEmpty());
    }

    /**
     * 测试 getNumber 方法，当传入的 TensorProto 的 dtype 为非法值时
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetNumberWithInvalidDtype() {
        // arrange
        // 使用 UNRECOGNIZED 的值 -1 来模拟一个无效的 dtype
        TensorProto proto = TensorProto.newBuilder().setDtypeValue(DataType.UNRECOGNIZED.getNumber()).build();
        // act
        TensorUtil.getNumber(proto);
        // assert 由于预期会抛出异常，所以不需要断言
    }

    /**
     * 测试 getNumber 方法，当传入的 TensorProto 的 dtype 为 DT_INT32 时
     */
    @Test
    public void testGetNumberWithDT_INT32() {
        // arrange
        TensorProto proto = TensorProto.newBuilder().setDtypeValue(DataType.DT_INT32_VALUE).addAllIntVal(Arrays.asList(1, 2, 3)).build();
        // act
        List<? extends Number> result = TensorUtil.getNumber(proto);
        // assert
        Assert.assertEquals(Arrays.asList(1, 2, 3), result);
    }

    /**
     * 测试 getNumber 方法，当传入的 TensorProto 的 dtype 为 DT_INT64 时
     */
    @Test
    public void testGetNumberWithDT_INT64() {
        // arrange
        TensorProto proto = TensorProto.newBuilder().setDtypeValue(DataType.DT_INT64_VALUE).addAllInt64Val(Arrays.asList(1L, 2L, 3L)).build();
        // act
        List<? extends Number> result = TensorUtil.getNumber(proto);
        // assert
        Assert.assertEquals(Arrays.asList(1L, 2L, 3L), result);
    }

    /**
     * 测试 getNumber 方法，当传入的 TensorProto 的 dtype 为 DT_FLOAT 时
     */
    @Test
    public void testGetNumberWithDT_FLOAT() {
        // arrange
        TensorProto proto = TensorProto.newBuilder().setDtypeValue(DataType.DT_FLOAT_VALUE).addAllFloatVal(Arrays.asList(1.0f, 2.0f, 3.0f)).build();
        // act
        List<? extends Number> result = TensorUtil.getNumber(proto);
        // assert
        Assert.assertEquals(Arrays.asList(1.0f, 2.0f, 3.0f), result);
    }

    /**
     * 测试 getNumber 方法，当传入的 TensorProto 的 dtype 为 DT_DOUBLE 时
     */
    @Test
    public void testGetNumberWithDT_DOUBLE() {
        // arrange
        TensorProto proto = TensorProto.newBuilder().setDtypeValue(DataType.DT_DOUBLE_VALUE).addAllDoubleVal(Arrays.asList(1.0, 2.0, 3.0)).build();
        // act
        List<? extends Number> result = TensorUtil.getNumber(proto);
        // assert
        Assert.assertEquals(Arrays.asList(1.0, 2.0, 3.0), result);
    }

    /**
     * 测试当维度大小不匹配列表大小时构建双精度张量
     */
    @Test
    public void testBuildDoubleTensorDimensionMismatch() {
        // arrange
        List<Double> list = Arrays.asList(1.0, 2.0, 3.0, 4.0);
        int dim1 = 2;
        // 不匹配的维度
        int dim2 = 3;
        // act
        TensorProto tensorProto = TensorUtil.buildDoubleTensor(list, dim1, dim2);
        // assert
        Assert.assertNotNull(tensorProto);
        // 由于被测代码没有抛出异常，这里不进行异常断言
    }

    /**
     * 测试当维度为负数时构建双精度张量
     */
    @Test
    public void testBuildDoubleTensorNegativeDimension() {
        // arrange
        List<Double> list = Arrays.asList(1.0, 2.0, 3.0, 4.0);
        // 负数维度
        int dim1 = -1;
        int dim2 = 2;
        // act
        TensorProto tensorProto = TensorUtil.buildDoubleTensor(list, dim1, dim2);
        // assert
        Assert.assertNotNull(tensorProto);
        // 由于被测代码没有抛出异常，这里不进行异常断言
    }
}
