package com.sankuai.meishi.stgy.algoplatform.predictor.mq;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictResponse;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.TPredictServicePublish;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
public class BmlDealTagListenerTest {

    @InjectMocks
    private BmlDealTagListener bmlDealTagListener;

    @Mock
    private TPredictServicePublish tPredictServicePublish;

    @Mock
    private IProducerProcessor bmlDealTagResultProducer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常情况下消息被成功处理
     */
    @Test
    public void testRecvMessageSuccess() throws Throwable {
        TPredictResponse resp = new TPredictResponse(0);
        resp.setData(ImmutableMap.of("code", "0", "data", "1"));
        doReturn(resp).when(tPredictServicePublish).predict(any(TPredictRequest.class));

        TPredictRequest req = new TPredictRequest();
        req.setReq(ImmutableMap.of("input_data", "{}"));
        req.setExtra(ImmutableMap.of("k1","v1"));

        // arrange
        String validMessageBody = JSON.toJSONString(req);
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, validMessageBody);
        MessagetContext messagetContext = new MessagetContext();
        // act
        ConsumeStatus result = bmlDealTagListener.recvMessage(message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试消息体无法解析为TPredictRequest时的情况
     */
    @Test
    public void testRecvMessageParseFailure() throws Throwable {
        // arrange
        String invalidMessageBody = "invalid JSON";
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, invalidMessageBody);
        MessagetContext messagetContext = new MessagetContext();
        // act
        ConsumeStatus result = bmlDealTagListener.recvMessage(message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.RECONSUME_LATER, result);
    }

    /**
     * 测试predictAsync方法抛出异常时的情况
     */
    @Test
    public void testRecvMessagePredictThrowsException() throws Throwable {
        // arrange
        String validMessageBody = "{\"someKey\":\"someValue\"}";
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, validMessageBody);
        MessagetContext messagetContext = new MessagetContext();
        doThrow(new RuntimeException()).when(tPredictServicePublish).predict(any(TPredictRequest.class));
        // act
        ConsumeStatus result = bmlDealTagListener.recvMessage(message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

}
