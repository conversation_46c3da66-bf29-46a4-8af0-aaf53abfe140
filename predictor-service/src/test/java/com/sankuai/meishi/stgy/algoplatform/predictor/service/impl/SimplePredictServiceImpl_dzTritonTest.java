package com.sankuai.meishi.stgy.algoplatform.predictor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;
import com.meituan.hadoop.afo.serving.client.thrift.MTThriftPredictClient;
import com.meituan.hadoop.afo.serving.common.OnlineService;
import com.meituan.hadoop.afo.triton.InferenceServerClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.TestCaseHelper;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl.PredictAppServiceImpl;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimpleFeatureService;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.TairClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.util.TritonUtil;
import inference.GrpcService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;


import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


/**
 * <AUTHOR>
 * @date 2024年05月14日 4:56 下午
 */
@RunWith(MockitoJUnitRunner.class)
public class SimplePredictServiceImpl_dzTritonTest {
    @InjectMocks
    private SimplePredictServiceImpl simplePredictService;
//
    @Mock
    private LoadingCache<String, InferenceServerClient> dzTritonPredictClientCache;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
//
//        // Use PowerMockito to mock the private final field
        simplePredictService = PowerMockito.spy(new SimplePredictServiceImpl());
        PowerMockito.field(SimplePredictServiceImpl.class, "newTritonPredictClientCache").set(simplePredictService, dzTritonPredictClientCache);
    }

    @Test
    public void parseOutputTest() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method parseOutput = TestCaseHelper.getPrivateMethodForTest(SimplePredictServiceImpl.class, "parseOutput", new Class[]{LinkedHashMap.class});
        Map<String, List<Object>> input = new LinkedHashMap<>();
        input.put("test", Lists.newArrayList("{\"test\":123}"));
        Object invoke = parseOutput.invoke(simplePredictService, new Object[]{input});
        Assert.assertNotNull(invoke);
    }

    @Test
    public void dzTritonPredictTest() {
        try {
            Method dzTritonPredict = TestCaseHelper.getPrivateMethodForTest(SimplePredictServiceImpl.class, "newTritonPredict", new Class[]{String.class, String.class, List.class});
            Object test = dzTritonPredict.invoke(simplePredictService, new Object[]{"", "", Lists.newArrayList("test")});
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }
    }

    @Test
    public void mergeOutputTest() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method mergeOutput = TestCaseHelper.getPrivateMethodForTest(SimplePredictServiceImpl.class, "mergeOutput", new Class[]{Map.class});
        Map<String, List<String>> inputMap = new HashMap<>();
        inputMap.put("test", Lists.newArrayList("1","2","3"));
        Map<String,List<?>> result = (Map<String, List<?>>) mergeOutput.invoke(simplePredictService, new Object[]{inputMap});
        Object test = result.get("test").get(0);
        Assert.assertEquals(Lists.newArrayList("1#2#3"), test);
    }
}
