package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import com.sankuai.meishi.stgy.algoplatform.predictor.util.SpringContextUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
@PrepareForTest({SpringContextUtils.class})
public class PythonConnectionTest {

    @Test
    public void threadStrategyTest() throws ClassNotFoundException {
        CustomThreadClassLoadingStrategy strategy = new CustomThreadClassLoadingStrategy();
        Class<?> clazz1 = strategy.classForName("JavaBridge");
        Class<?> clazz2 = strategy.classForName(JavaBridge.class.getName());
        Assert.assertSame(clazz1, clazz2);
    }
}
