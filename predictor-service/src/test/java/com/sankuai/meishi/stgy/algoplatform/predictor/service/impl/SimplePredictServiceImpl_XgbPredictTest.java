package com.sankuai.meishi.stgy.algoplatform.predictor.service.impl;

import com.google.common.cache.LoadingCache;
import com.meituan.hadoop.afo.jpmmlclient.JpmmlPredictClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimplePredictService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

/**
 * 测试SimplePredictServiceImpl的xgbPredict方法
 */
@RunWith(MockitoJUnitRunner.class)
public class SimplePredictServiceImpl_XgbPredictTest {

    @Mock
    private JpmmlPredictClient mockJpmmlPredictClient;

    @Mock
    private LoadingCache<String, JpmmlPredictClient> mockXgbPredictClientCache;

    @InjectMocks
    private SimplePredictServiceImpl simplePredictService;

    @Before
    public void setUp() throws Exception {
        // 初始化mock对象
        when(mockJpmmlPredictClient.batchPredictByXGB(anyList())).thenReturn(new double[][] { { 0.1, 0.2 }, { 0.3, 0.4 } });
        when(mockJpmmlPredictClient.batchPredictByXGB(anyList(), anyString())).thenReturn(new double[][] { { 0.5, 0.6 }, { 0.7, 0.8 } });
        when(mockXgbPredictClientCache.get(anyString())).thenReturn(mockJpmmlPredictClient);
        // 使用反射注入mockXgbPredictClientCache
        Field xgbPredictClientCacheField = SimplePredictServiceImpl.class.getDeclaredField("xgbPredictClientCache");
        xgbPredictClientCacheField.setAccessible(true);
        xgbPredictClientCacheField.set(simplePredictService, mockXgbPredictClientCache);
    }

    /**
     * 测试featuresMapList为空的情况
     */
    @Test
    public void testXgbPredict_EmptyFeaturesMapList() {
        // arrange
        String modelName = "testModel";
        List<Map<String, Double>> featuresMapList = Collections.emptyList();
        String defaultValue = "0";
        // act
        List<List<Double>> result = simplePredictService.xgbPredict(modelName, featuresMapList, defaultValue);
        // assert
        assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试正常情况，不带defaultValue
     */
    @Test
    public void testXgbPredict_NormalWithoutDefaultValue() throws Exception {
        // arrange
        String modelName = "testModel";
        List<Map<String, Double>> featuresMapList = Collections.singletonList(Collections.singletonMap("feature", 1.0));
        String defaultValue = null;
        // act
        List<List<Double>> result = simplePredictService.xgbPredict(modelName, featuresMapList, defaultValue);
        // assert
        assertNotNull("结果不应为空", result);
        assertEquals("结果列表大小应为2", 2, result.size());
        assertArrayEquals("第一个结果应为[0.1, 0.2]", new Double[] { 0.1, 0.2 }, result.get(0).toArray(new Double[0]));
        assertArrayEquals("第二个结果应为[0.3, 0.4]", new Double[] { 0.3, 0.4 }, result.get(1).toArray(new Double[0]));
    }

    /**
     * 测试正常情况，带defaultValue
     */
    @Test
    public void testXgbPredict_NormalWithDefaultValue() throws Exception {
        // arrange
        String modelName = "testModel";
        List<Map<String, Double>> featuresMapList = Collections.singletonList(Collections.singletonMap("feature", 1.0));
        String defaultValue = "0";
        // act
        List<List<Double>> result = simplePredictService.xgbPredict(modelName, featuresMapList, defaultValue);
        // assert
        assertNotNull("结果不应为空", result);
        assertEquals("结果列表大小应为2", 2, result.size());
        assertArrayEquals("第一个结果应为[0.5, 0.6]", new Double[] { 0.5, 0.6 }, result.get(0).toArray(new Double[0]));
        assertArrayEquals("第二个结果应为[0.7, 0.8]", new Double[] { 0.7, 0.8 }, result.get(1).toArray(new Double[0]));
    }

    /**
     * 测试当JpmmlPredictClient抛出异常的情况
     */
    @Test(expected = RuntimeException.class)
    public void testXgbPredict_JpmmlPredictClientThrowsException() throws Exception {
        // arrange
        String modelName = "testModel";
        List<Map<String, Double>> featuresMapList = Collections.singletonList(Collections.singletonMap("feature", 1.0));
        String defaultValue = "0";
        when(mockJpmmlPredictClient.batchPredictByXGB(anyList(), anyString())).thenThrow(new RuntimeException("Mock Exception"));
        // act
        simplePredictService.xgbPredict(modelName, featuresMapList, defaultValue);
        // assert
        // 预期抛出RuntimeException
    }
}
