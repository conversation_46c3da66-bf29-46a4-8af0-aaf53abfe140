package com.sankuai.meishi.stgy.algoplatform.predictor.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sankuai.meishi.stgy.algoplatform.predictor.BaseTest;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
public class SimpleFeatureServiceTest {
//    @Resource
    private SimpleFeatureService featureService;

    //    @Test
    public void queryTest() {
        List<Map<String, String>> res = featureService.query("1482", ImmutableList.of(
                ImmutableMap.of(
                        "passengerid", "11"
                )));
        log.info("res: {}", JSONObject.toJSONString(res));
    }
}