package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackage;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.AlgoPackageDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoPackagePo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AlgoPackageServiceImpl_RefreshTest {

    @Mock
    private AlgoPackageDao algoPackageDao;

    @Mock
    private AlgoPackage algoPackage;

    private AlgoPackageServiceImpl algoPackageService;

    private Map<Long, AlgoPackage> loadedPackages;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        algoPackageService = new AlgoPackageServiceImpl();
        loadedPackages = new ConcurrentHashMap<>();
        // 使用反射设置私有成员
        Field algoPackageDaoField = AlgoPackageServiceImpl.class.getDeclaredField("algoPackageDao");
        algoPackageDaoField.setAccessible(true);
        algoPackageDaoField.set(algoPackageService, algoPackageDao);
        Field loadedPackagesField = AlgoPackageServiceImpl.class.getDeclaredField("loadedPackages");
        loadedPackagesField.setAccessible(true);
        loadedPackagesField.set(algoPackageService, loadedPackages);
    }

    /**
     * 测试场景：当数据库中没有有效的算法包时，应该返回0。
     */
    @Test
    public void testRefreshWhenNoValidPackages() throws Throwable {
        // arrange
        when(algoPackageDao.getValidPackages()).thenReturn(new ArrayList<>());
        // act
        int result = algoPackageService.refresh(false);
        // assert
        assertEquals(0, result);
    }
}
