package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import com.dianping.lion.client.ConfigRepository;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.AlgoPackageDao;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import java.lang.reflect.Field;
import org.junit.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.google.common.base.Preconditions;
import org.apache.commons.lang3.StringUtils;
import static org.junit.Assert.*;
import org.mockito.InjectMocks.*;

@RunWith(MockitoJUnitRunner.class)
public class AlgoPackageServiceImpl_InitTest {

    @Mock
    private AlgoPackageDao algoPackageDao;

    @Mock
    private ConfigRepository configRepository;

    private AlgoPackageServiceImpl algoPackageService;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        algoPackageService = new AlgoPackageServiceImpl();
        // 使用反射设置私有属性
        Field algoPackageDaoField = AlgoPackageServiceImpl.class.getDeclaredField("algoPackageDao");
        algoPackageDaoField.setAccessible(true);
        algoPackageDaoField.set(algoPackageService, algoPackageDao);
        Field pythonRuntimeField = AlgoPackageServiceImpl.class.getDeclaredField("pythonRuntime");
        pythonRuntimeField.setAccessible(true);
        pythonRuntimeField.set(algoPackageService, "ok");
    }

    // 其他测试方法保持不变
    // ...
    /**
     * 测试 init 方法，当 pythonRuntime 不为 "ok" 时应抛出 IllegalStateException
     */
    @Test(expected = IllegalStateException.class)
    public void testInit_WithPythonRuntimeNotOk() throws NoSuchFieldException, IllegalAccessException {
        // arrange
        Field pythonRuntimeField = AlgoPackageServiceImpl.class.getDeclaredField("pythonRuntime");
        pythonRuntimeField.setAccessible(true);
        pythonRuntimeField.set(algoPackageService, "not_ok");
        // act
        algoPackageService.init();
        // assert
        // Expected exception
    }

    /**
     * 测试 init 方法，当 THROW_ALGOPACK_INIT_ERROR 配置为 false 时
     */
    @Test
    public void testInit_WithThrowErrorFalse() {
        // arrange
        // 不需要再次设置返回值，因为在 setUp 中已经设置了默认返回 false
        // act
        algoPackageService.init();
        // assert
        // 不需要验证 getBooleanValue 的调用，因为当 pythonRuntime 为 "ok" 且 THROW_ALGOPACK_INIT_ERROR 配置为 false 时，init 方法不会抛出异常，也不会调用 getBooleanValue
    }
}
