package com.sankuai.meishi.stgy.algoplatform.predictor.service.util;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.protobuf.ByteString;
import inference.GrpcService;
import java.nio.LongBuffer;
import java.util.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class TritonUtilTest {

    private GrpcService.ModelInferRequest.Builder requestBuilder;

    private LinkedHashMap<String, List<Object>> inputValue;

    private Map<String, List<Integer>> inputShape;

    private Map<String, String> inputType;

    private List<String> output;

    /**
     * 测试 toArray 方法，当 LongBuffer 有数组支持且没有偏移量时
     */
    @Test
    public void testToArray_NoOffset() {
        // arrange
        long[] expectedArray = new long[] { 1, 2, 3 };
        LongBuffer buffer = LongBuffer.wrap(expectedArray);
        // act
        long[] actualArray = TritonUtil.toArray(buffer);
        // assert
        Assert.assertArrayEquals(expectedArray, actualArray);
    }

    /**
     * 测试 toArray 方法，当 LongBuffer 没有数组支持时
     */
    @Test
    public void testToArray_NoArraySupport() {
        // arrange
        long[] expectedArray = new long[] { 1, 2, 3 };
        LongBuffer buffer = LongBuffer.allocate(3);
        buffer.put(expectedArray);
        buffer.flip();
        // act
        long[] actualArray = TritonUtil.toArray(buffer);
        // assert
        Assert.assertArrayEquals(expectedArray, actualArray);
    }

    /**
     * 测试 toArray 方法，当 LongBuffer 为空时
     */
    @Test
    public void testToArray_EmptyBuffer() {
        // arrange
        long[] expectedArray = new long[] {};
        LongBuffer buffer = LongBuffer.allocate(0);
        // act
        long[] actualArray = TritonUtil.toArray(buffer);
        // assert
        Assert.assertArrayEquals(expectedArray, actualArray);
    }

    @Before
    public void setUp() {
        requestBuilder = GrpcService.ModelInferRequest.newBuilder();
        inputValue = new LinkedHashMap<>();
        inputShape = new HashMap<>();
        inputType = new HashMap<>();
        output = new ArrayList<>();
    }

    /**
     * 测试buildTensorByShape方法，正常情况
     */
    @Test
    public void testBuildTensorByShapeNormal() {
        // arrange
        inputValue.put("input1", Arrays.asList(1, 2, 3));
        inputShape.put("input1", Arrays.asList(3));
        inputType.put("input1", "int");
        output.add("output1");
        // act
        GrpcService.ModelInferRequest.Builder result = TritonUtil.buildTensorByShape(requestBuilder, inputValue, inputShape, inputType, output);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getInputsCount());
        assertEquals(1, result.getOutputsCount());
        assertEquals("INT32", result.getInputs(0).getDatatype());
    }

    /**
     * 测试buildTensorByShape方法，当dataType为null时抛出NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testBuildTensorByShapeWithNullDataType() {
        // arrange
        inputValue.put("input1", Arrays.asList(1, 2, 3));
        inputShape.put("input1", Arrays.asList(3));
        inputType.put("input1", null);
        output.add("output1");
        // act
        TritonUtil.buildTensorByShape(requestBuilder, inputValue, inputShape, inputType, output);
        // assert is handled by the expected exception
    }

    /**
     * 测试buildTensorByShape方法，当dataType不支持时抛出IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testBuildTensorByShapeWithUnsupportedDataType() {
        // arrange
        inputValue.put("input1", Arrays.asList(1, 2, 3));
        inputShape.put("input1", Arrays.asList(3));
        inputType.put("input1", "unsupported");
        output.add("output1");
        // act
        TritonUtil.buildTensorByShape(requestBuilder, inputValue, inputShape, inputType, output);
        // assert is handled by the expected exception
    }

    /**
     * 测试buildTensorByShape方法，当inputValue为空时
     */
    @Test
    public void testBuildTensorByShapeWithEmptyInputValue() {
        // arrange
        output.add("output1");
        // act
        GrpcService.ModelInferRequest.Builder result = TritonUtil.buildTensorByShape(requestBuilder, inputValue, inputShape, inputType, output);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getInputsCount());
        assertEquals(1, result.getOutputsCount());
    }

    /**
     * 测试buildTensorByShape方法，当output为空时
     */
    @Test
    public void testBuildTensorByShapeWithEmptyOutput() {
        // arrange
        inputValue.put("input1", Arrays.asList(1, 2, 3));
        inputShape.put("input1", Arrays.asList(3));
        inputType.put("input1", "int");
        // act
        GrpcService.ModelInferRequest.Builder result = TritonUtil.buildTensorByShape(requestBuilder, inputValue, inputShape, inputType, output);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getInputsCount());
        assertEquals(0, result.getOutputsCount());
    }
}
