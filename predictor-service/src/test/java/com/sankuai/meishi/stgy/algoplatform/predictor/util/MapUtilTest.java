package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import org.junit.Test;
import org.junit.Assert;
import java.util.*;

public class MapUtilTest {

    /**
     * 测试size小于等于0
     */
    @Test
    public void testPartitionSizeLessThanOrEqualToZero() {
        Map<String, String> map = new HashMap<>();
        map.put("key1", "value1");
        List<Map<String, String>> result = MapUtil.partition(map, 0);
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("结果列表应只有一个元素", 1, result.size());
        Assert.assertEquals("结果Map应与原Map相同", map, result.get(0));
    }

    // 其他测试方法保持不变...
    /**
     * 测试空Map
     */
    @Test
    public void testPartitionEmptyMap() {
        Map<String, String> map = Collections.emptyMap();
        List<Map<String, String>> result = MapUtil.partition(map, 1);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    // 其他测试方法保持不变...
    /**
     * 测试Map大小小于等于size
     */
    @Test
    public void testPartitionMapSizeLessThanOrEqualToSize() {
        Map<String, String> map = new HashMap<>();
        map.put("key1", "value1");
        List<Map<String, String>> result = MapUtil.partition(map, 1);
        Assert.assertEquals("结果列表应只有一个元素", 1, result.size());
        Assert.assertEquals("结果Map应与原Map相同", map, result.get(0));
    }

    /**
     * 测试Map大小大于size，且能整除size
     */
    @Test
    public void testPartitionMapSizeGreaterThanSizeAndDivisible() {
        Map<String, String> map = new HashMap<>();
        for (int i = 1; i <= 4; i++) {
            map.put("key" + i, "value" + i);
        }
        List<Map<String, String>> result = MapUtil.partition(map, 2);
        Assert.assertEquals("结果列表应有两个元素", 2, result.size());
        Assert.assertEquals("第一个分区Map大小应为2", 2, result.get(0).size());
        Assert.assertEquals("第二个分区Map大小应为2", 2, result.get(1).size());
    }

    /**
     * 测试Map大小大于size，且不能整除size
     */
    @Test
    public void testPartitionMapSizeGreaterThanSizeAndNotDivisible() {
        Map<String, String> map = new HashMap<>();
        for (int i = 1; i <= 5; i++) {
            map.put("key" + i, "value" + i);
        }
        List<Map<String, String>> result = MapUtil.partition(map, 2);
        Assert.assertEquals("结果列表应有三个元素", 3, result.size());
        Assert.assertEquals("最后一个分区Map大小应为1", 1, result.get(2).size());
    }

    /**
     * 测试Map为null
     */
    @Test
    public void testPartitionMapIsNull() {
        List<Map<String, String>> result = MapUtil.partition(null, 1);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试size大于Map大小
     */
    @Test
    public void testPartitionSizeGreaterThanMapSize() {
        Map<String, String> map = new HashMap<>();
        map.put("key1", "value1");
        List<Map<String, String>> result = MapUtil.partition(map, 10);
        Assert.assertEquals("结果列表应只有一个元素", 1, result.size());
        Assert.assertEquals("结果Map应与原Map相同", map, result.get(0));
    }

    /**
     * 测试Map包含null值
     */
    @Test
    public void testPartitionMapContainsNullValue() {
        Map<String, String> map = new HashMap<>();
        map.put("key1", null);
        map.put("key2", "value2");
        List<Map<String, String>> result = MapUtil.partition(map, 1);
        Assert.assertEquals("结果列表应有两个元素", 2, result.size());
        Assert.assertNull("第一个分区Map应包含null值", result.get(0).get("key1"));
    }
}
