package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.google.gson.Gson;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimpleFeatureService;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimplePredictService;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.SpringContextUtils;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.msgpack.core.MessageBufferPacker;
import org.msgpack.core.MessagePack;
import org.msgpack.core.MessageUnpacker;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({ "javax.management.*", "javax.net.ssl.*" })
@PrepareForTest({ SpringContextUtils.class })
public class JavaBridgeTest {

    private static final String ORIGINAL_NAME = "World";

    private SimpleFeatureService mockFeatureService = mock(SimpleFeatureService.class);

    @Mock
    private RaptorTrack raptorTrack;

    private static ConfigRepository originalConfigRepository;

    private TestConfigRepository testConfigRepository;

    @Before
    public void before() throws Exception {
        PowerMockito.mockStatic(SpringContextUtils.class);
    }

    @After
    public void resetMocks() {
        reset(mockFeatureService);
    }

    @Before
    public void setUp() {
        // Store the original ConfigRepository
        originalConfigRepository = Lion.getConfigRepository();
        testConfigRepository = new TestConfigRepository();
    }

    private void extractedTest(List<Double> doubles, List<List<Double>> values, int batchSize) throws IOException {
        SimplePredictService simplePredictService = Mockito.mock(SimplePredictService.class);
        Mockito.when(simplePredictService.xgbPredictByMatrix(Mockito.anyString(), Mockito.anyList())).thenReturn(values);
        Mockito.when(SpringContextUtils.getBean(SimplePredictService.class)).thenReturn(simplePredictService);
        MessageBufferPacker packer = MessagePack.newDefaultBufferPacker();
        packer.packArrayHeader(doubles.size());
        for (double e : doubles) {
            packer.packDouble(e);
        }
        byte[] inputBytes = packer.toByteArray();
        byte[] valueBytes = JavaBridge.predictXGBByMatrix("test", inputBytes, batchSize, "");
        MessageUnpacker unpacker = MessagePack.newDefaultUnpacker(valueBytes);
        int headerSize = unpacker.unpackArrayHeader();
        List<Double> res = new ArrayList<>();
        for (int i = 0; i < headerSize; i++) {
            res.add(unpacker.unpackDouble());
        }
        List<Double> expected = values.stream().flatMap(Collection::stream).collect(Collectors.toList());
        assert headerSize == expected.size();
        assert expected.equals(res);
    }

    private void setTestConfigRepository(TestConfigRepository repository) {
        try {
            java.lang.reflect.Field field = Lion.class.getDeclaredField("repositoryManager");
            field.setAccessible(true);
            field.set(null, new TestConfigRepositoryManager(repository));
        } catch (Exception e) {
            throw new RuntimeException("Failed to set test ConfigRepository", e);
        }
    }

    @Test
    public void predictXGBByMatrixTest() throws IOException {
        List<Double> doubles = Arrays.asList(0.0, 0.1, 0.2, 0.3, 0.4, 0.5);
        List<List<Double>> values = Arrays.asList(Arrays.asList(0.1, 0.1), Arrays.asList(0.2, 0.2));
        int batchSize = values.size();
        extractedTest(doubles, values, batchSize);
    }

    @Test
    public void predictXGBByMatrixTest2() throws IOException {
        List<Double> doubles = Arrays.asList(0.0);
        List<List<Double>> values = Arrays.asList(Arrays.asList(0.1));
        int batchSize = values.size();
        extractedTest(doubles, values, batchSize);
    }

    @Test
    public void predictXGBByMatrixTest3() throws IOException {
        List<Double> doubles = Arrays.asList(0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8);
        List<List<Double>> values = Arrays.asList(Arrays.asList(0.1), Arrays.asList(0.2), Arrays.asList(0.3));
        int batchSize = values.size();
        extractedTest(doubles, values, batchSize);
    }

    /**
     * 测试 sayHello 方法，传入空字符串
     */
    @Test
    public void testSayHelloWithEmptyString() {
        // arrange
        String name = "";
        // act
        String result = JavaBridge.sayHello(name);
        // assert
        assertEquals("hello:", result);
    }

    /**
     * 测试 queryFeature 方法，输入 JSON 解析异常
     */
    @Test(expected = com.alibaba.fastjson.JSONException.class)
    public void testQueryFeatureJsonParseException() {
        // arrange
        String groupId = "testGroup";
        String invalidInputJson = "invalid json";
        // act
        JavaBridge.queryFeature(groupId, invalidInputJson);
        // assert is handled by the expected exception
    }

    /**
     * 测试invokeThrift方法，正常情况
     */
    @Test
    public void testInvokeThriftNormal() throws Exception {
        // arrange
        String appKey = "appKey";
        String serviceName = "serviceName";
        String methodName = "methodName";
        String paramsJson = "[\"param1\",\"param2\"]";
        //        List<String> params = new Gson().fromJson(paramsJson, new TypeToken<List<String>>() {
        //        }.getType());
        //        String expectedResult = "success";
        // act
        String result = JavaBridge.invokeThrift(appKey, serviceName, methodName, paramsJson);
        // assert
        //        Map<String, String> expectedMap = new HashMap<>();
        //        expectedMap.put("code", "0");
        //        expectedMap.put("data", expectedResult);
        // 由于无法mock ThriftUtil.invoke的返回值，这里的断言可能需要根据实际情况调整
        Assert.assertNotNull(new Gson().fromJson(result, Map.class).get("code"));
    }

    /**
     * 测试invokeThrift方法，参数JSON格式错误
     */
    @Test(expected = Exception.class)
    public void testInvokeThriftInvalidJson() throws Exception {
        // arrange
        String appKey = "appKey";
        String serviceName = "serviceName";
        String methodName = "methodName";
        String invalidParamsJson = "not a json string";
        // act
        JavaBridge.invokeThrift(appKey, serviceName, methodName, invalidParamsJson);
        // assert is handled by the expected exception
    }

    /**
     * 测试 tritonPredict 方法抛出异常情况
     */
    @Test(expected = RuntimeException.class)
    public void testTritonPredictException() throws Exception {
        SimplePredictService mockSimplePredictService = mock(SimplePredictService.class);
        when(mockSimplePredictService.tritonPredict(any(), any(), any(), any(), any(), any())).thenThrow(new RuntimeException("Service not available"));
        JavaBridge.tritonPredict("modelName", "modelVersion", "", "", "", "");
    }

    @Test(expected = NullPointerException.class)
    public void testReadTair() {
        JavaBridge.readTair("");
    }

    private static class TestConfigRepository extends ConfigRepository {

        private final java.util.Map<String, String> values = new java.util.HashMap<>();

        private boolean throwException = false;

        public TestConfigRepository() {
            super("test", "test");
        }

        @Override
        public String get(String name, String defaultValue) {
            if (throwException) {
                throw new RuntimeException("Test exception");
            }
            return values.getOrDefault(name, defaultValue);
        }

        @Override
        public boolean setValue(String key, String value) {
            values.put(key, value);
            return true;
        }

        public void setTestValue(String key, String value) {
            values.put(key, value);
        }

        public void setThrowException(boolean throwException) {
            this.throwException = throwException;
        }
    }

    private static class TestConfigRepositoryManager {

        private final TestConfigRepository repository;

        public TestConfigRepositoryManager(TestConfigRepository repository) {
            this.repository = repository;
        }

        public ConfigRepository getConfigRepository(String appkey, String group) {
            return repository;
        }
    }

    /**
     * 测试 readLion 方法，当 Lion.getConfigRepository().get 抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testReadLion_ExceptionCase() throws Throwable {
        // arrange
        String key = "testKey";
        String defaultValue = "testDefaultValue";
        testConfigRepository.setThrowException(true);
        setTestConfigRepository(testConfigRepository);
        // act
        JavaBridge.readLion(key, defaultValue);
    }

    @Test
    public void testLogMetric_正常场景() {
        // 准备测试数据
        String name = "test_metric";
        String tagsJson = "{\"tag1\":\"value1\",\"tag2\":\"value2\"}";
        Integer count = 1;

        // 执行测试
        JavaBridge.logMetric(name, tagsJson, count);
        // 由于RaptorTrack是静态方法,这里主要验证不抛异常即可
    }

    /**
     * 测试 pigeonInvoke 方法 - 正常情况
     */
    @Test
    public void testPigeonInvoke_Success() throws Exception {
        // arrange
        String serviceInfo = "{\"appKey\":\"com.sankuai.test\",\"interfaceName\":\"com.test.Service\",\"methodName\":\"testMethod\",\"timeout\":5000}";
        List<String> paramsType = Arrays.asList("java.lang.String", "java.lang.Integer");
        List<String> paramValues = Arrays.asList("testParam", "123");
        String expectedResult = "success";

        // mock dependencies
        com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceFactory mockPigeonServiceFactory =
            mock(com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceFactory.class);
        com.dianping.pigeon.remoting.common.service.GenericService mockGenericService =
            mock(com.dianping.pigeon.remoting.common.service.GenericService.class);

        when(SpringContextUtils.getBean(com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceFactory.class))
            .thenReturn(mockPigeonServiceFactory);
        when(mockPigeonServiceFactory.createPigeonProxy(any())).thenReturn(mockGenericService);
        when(mockGenericService.$invoke(eq("testMethod"), eq(paramsType), eq(paramValues)))
            .thenReturn(expectedResult);

        // act
        String result = JavaBridge.pigeonInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertEquals(expectedResult, result);
        verify(mockPigeonServiceFactory).createPigeonProxy(any());
        verify(mockGenericService).$invoke("testMethod", paramsType, paramValues);
    }

    /**
     * 测试 pigeonInvoke 方法 - JSON解析异常
     */
    @Test
    public void testPigeonInvoke_InvalidJson() {
        // arrange
        String invalidServiceInfo = "invalid json";
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        // act
        String result = JavaBridge.pigeonInvoke(invalidServiceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试 pigeonInvoke 方法 - 服务调用异常
     */
    @Test
    public void testPigeonInvoke_ServiceException() throws Exception {
        // arrange
        String serviceInfo = "{\"appKey\":\"com.sankuai.test\",\"interfaceName\":\"com.test.Service\",\"methodName\":\"testMethod\",\"timeout\":5000}";
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        // mock dependencies
        com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceFactory mockPigeonServiceFactory =
            mock(com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceFactory.class);
        com.dianping.pigeon.remoting.common.service.GenericService mockGenericService =
            mock(com.dianping.pigeon.remoting.common.service.GenericService.class);

        when(SpringContextUtils.getBean(com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceFactory.class))
            .thenReturn(mockPigeonServiceFactory);
        when(mockPigeonServiceFactory.createPigeonProxy(any())).thenReturn(mockGenericService);
        when(mockGenericService.$invoke(anyString(), anyList(), anyList()))
            .thenThrow(new RuntimeException("Service call failed"));

        // act
        String result = JavaBridge.pigeonInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试 thriftInvoke 方法 - 正常情况
     */
    @Test
    public void testThriftInvoke_Success() throws Exception {
        // arrange
        String serviceInfo = "{\"appKey\":\"com.sankuai.test\",\"interfaceName\":\"com.test.ThriftService\",\"methodName\":\"testMethod\",\"timeOut\":5000}";
        List<String> paramsType = Arrays.asList("java.lang.String", "java.lang.Integer");
        List<String> paramValues = Arrays.asList("testParam", "123");
        String expectedResult = "thrift success";

        // mock dependencies
        com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftClientProxyBeanConfig mockThriftConfig =
            mock(com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftClientProxyBeanConfig.class);
        com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy mockThriftProxy =
            mock(com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy.class);
        com.meituan.service.mobile.mtthrift.generic.GenericService mockGenericService =
            mock(com.meituan.service.mobile.mtthrift.generic.GenericService.class);

        when(SpringContextUtils.getBean(com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftClientProxyBeanConfig.class))
            .thenReturn(mockThriftConfig);
        when(mockThriftConfig.createThriftProxy(any())).thenReturn(mockThriftProxy);
        when(mockThriftProxy.getObject()).thenReturn(mockGenericService);
        when(mockGenericService.$invoke(eq("testMethod"), eq(paramsType), eq(paramValues)))
            .thenReturn(expectedResult);

        // act
        Object result = JavaBridge.thriftInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertEquals(expectedResult, result);
        verify(mockThriftConfig).createThriftProxy(any());
        verify(mockThriftProxy).getObject();
        verify(mockGenericService).$invoke("testMethod", paramsType, paramValues);
    }

    /**
     * 测试 thriftInvoke 方法 - JSON解析异常
     */
    @Test
    public void testThriftInvoke_InvalidJson() {
        // arrange
        String invalidServiceInfo = "invalid json";
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        // act
        Object result = JavaBridge.thriftInvoke(invalidServiceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试 thriftInvoke 方法 - 服务调用异常
     */
    @Test
    public void testThriftInvoke_ServiceException() throws Exception {
        // arrange
        String serviceInfo = "{\"appKey\":\"com.sankuai.test\",\"interfaceName\":\"com.test.ThriftService\",\"methodName\":\"testMethod\",\"timeOut\":5000}";
        List<String> paramsType = Arrays.asList("java.lang.String");
        List<String> paramValues = Arrays.asList("testParam");

        // mock dependencies
        com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftClientProxyBeanConfig mockThriftConfig =
            mock(com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftClientProxyBeanConfig.class);

        when(SpringContextUtils.getBean(com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftClientProxyBeanConfig.class))
            .thenReturn(mockThriftConfig);
        when(mockThriftConfig.createThriftProxy(any()))
            .thenThrow(new RuntimeException("Thrift proxy creation failed"));

        // act
        Object result = JavaBridge.thriftInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertNull(result);
    }

    /**
     * 测试 thriftInvoke 方法 - 空参数列表
     */
    @Test
    public void testThriftInvoke_EmptyParams() throws Exception {
        // arrange
        String serviceInfo = "{\"appKey\":\"com.sankuai.test\",\"interfaceName\":\"com.test.ThriftService\",\"methodName\":\"testMethod\",\"timeOut\":5000}";
        List<String> paramsType = Collections.emptyList();
        List<String> paramValues = Collections.emptyList();
        String expectedResult = "empty params result";

        // mock dependencies
        com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftClientProxyBeanConfig mockThriftConfig =
            mock(com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftClientProxyBeanConfig.class);
        com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy mockThriftProxy =
            mock(com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy.class);
        com.meituan.service.mobile.mtthrift.generic.GenericService mockGenericService =
            mock(com.meituan.service.mobile.mtthrift.generic.GenericService.class);

        when(SpringContextUtils.getBean(com.sankuai.meishi.stgy.algoplatform.predictor.thrift.ThriftClientProxyBeanConfig.class))
            .thenReturn(mockThriftConfig);
        when(mockThriftConfig.createThriftProxy(any())).thenReturn(mockThriftProxy);
        when(mockThriftProxy.getObject()).thenReturn(mockGenericService);
        when(mockGenericService.$invoke(eq("testMethod"), eq(paramsType), eq(paramValues)))
            .thenReturn(expectedResult);

        // act
        Object result = JavaBridge.thriftInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertEquals(expectedResult, result);
    }

    /**
     * 测试 pigeonInvoke 方法 - 空参数列表
     */
    @Test
    public void testPigeonInvoke_EmptyParams() throws Exception {
        // arrange
        String serviceInfo = "{\"appKey\":\"com.sankuai.test\",\"interfaceName\":\"com.test.Service\",\"methodName\":\"testMethod\",\"timeout\":5000}";
        List<String> paramsType = Collections.emptyList();
        List<String> paramValues = Collections.emptyList();
        String expectedResult = "empty params result";

        // mock dependencies
        com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceFactory mockPigeonServiceFactory =
            mock(com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceFactory.class);
        com.dianping.pigeon.remoting.common.service.GenericService mockGenericService =
            mock(com.dianping.pigeon.remoting.common.service.GenericService.class);

        when(SpringContextUtils.getBean(com.sankuai.meishi.stgy.algoplatform.predictor.pigeon.PigeonServiceFactory.class))
            .thenReturn(mockPigeonServiceFactory);
        when(mockPigeonServiceFactory.createPigeonProxy(any())).thenReturn(mockGenericService);
        when(mockGenericService.$invoke(eq("testMethod"), eq(paramsType), eq(paramValues)))
            .thenReturn(expectedResult);

        // act
        String result = JavaBridge.pigeonInvoke(serviceInfo, paramsType, paramValues);

        // assert
        assertEquals(expectedResult, result);
    }
}
