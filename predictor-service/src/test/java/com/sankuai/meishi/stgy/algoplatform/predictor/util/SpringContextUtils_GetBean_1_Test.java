package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import java.lang.reflect.Field;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.mockito.InjectMocks.*;

@RunWith(MockitoJUnitRunner.class)
public class SpringContextUtils_GetBean_1_Test {

    @Mock
    private ApplicationContext mockApplicationContext;

    private SpringContextUtils springContextUtils;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        springContextUtils = new SpringContextUtils();
        Field field = SpringContextUtils.class.getDeclaredField("applicationContext");
        field.setAccessible(true);
        field.set(null, mockApplicationContext);
    }

    @Test
    public void testGetBean_WithExistingBean() throws BeansException {
        // arrange
        Class<String> beanClass = String.class;
        String expectedBean = "testBean";
        when(mockApplicationContext.getBean(beanClass)).thenReturn(expectedBean);
        // act
        String actualBean = SpringContextUtils.getBean(beanClass);
        // assert
        assertEquals("应返回上下文中的 bean 实例", expectedBean, actualBean);
    }

    @Test(expected = BeansException.class)
    public void testGetBean_WithNonExistingBean() throws BeansException {
        // arrange
        Class<String> beanClass = String.class;
        when(mockApplicationContext.getBean(beanClass)).thenThrow(new BeansException("No bean of type " + beanClass.getName() + " available") {
        });
        // act
        SpringContextUtils.getBean(beanClass);
        // assert is handled by the expected exception
    }

    @Test(expected = NullPointerException.class)
    public void testGetBean_WithNullApplicationContext() throws BeansException, NoSuchFieldException, IllegalAccessException {
        // arrange
        Field field = SpringContextUtils.class.getDeclaredField("applicationContext");
        field.setAccessible(true);
        field.set(null, null);
        Class<String> beanClass = String.class;
        // act
        SpringContextUtils.getBean(beanClass);
        // assert is handled by the expected exception
    }
}
