package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import org.springframework.context.ApplicationContext;
import java.lang.reflect.Field;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

public class SpringContextUtils_GetApplicationContextTest {

    private static ApplicationContext mockApplicationContext;

    @BeforeClass
    public static void setUpClass() throws Exception {
        // 初始化一个模拟的ApplicationContext
        mockApplicationContext = mock(ApplicationContext.class);
        // 通过反射设置静态变量applicationContext的值
        setStaticApplicationContext(mockApplicationContext);
    }

    /**
     * 测试getApplicationContext方法在applicationContext被正确设置时的行为
     */
    @Test
    public void testGetApplicationContext_Normal() {
        // arrange 已通过@BeforeClass设置好环境
        // act
        ApplicationContext result = SpringContextUtils.getApplicationContext();
        // assert
        Assert.assertEquals("应该返回设置的ApplicationContext实例", mockApplicationContext, result);
    }

    /**
     * 测试getApplicationContext方法在applicationContext未被设置时的行为
     */
    @Test
    public void testGetApplicationContext_NotSet() throws Exception {
        // arrange
        setStaticApplicationContext(null);
        // act
        ApplicationContext result = SpringContextUtils.getApplicationContext();
        // assert
        Assert.assertNull("当ApplicationContext未被设置时，应该返回null", result);
    }

    private static void setStaticApplicationContext(ApplicationContext applicationContext) throws Exception {
        Field field = SpringContextUtils.class.getDeclaredField("applicationContext");
        field.setAccessible(true);
        field.set(null, applicationContext);
    }
}
