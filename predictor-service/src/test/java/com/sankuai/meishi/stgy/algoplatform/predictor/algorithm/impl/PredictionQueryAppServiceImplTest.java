package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.google.common.collect.ImmutableMap;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictionQueryAppService;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictionQueryContext;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.PredictionDataScriptDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.PredictionDataScriptPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.HorizonStrategyService;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.TairClient;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PredictionQueryAppServiceImplTest {

    @Mock
    private PredictionDataScriptDao dataScriptDao;

    @Mock
    private TairClient tairClient;

    @Mock
    private HorizonStrategyService horizonStrategyService;

    @InjectMocks
    private PredictionQueryAppServiceImpl predictionQueryAppService;

    @Before
    public void before() {
        MockitoAnnotations.initMocks(this);
    }

    @After
    public void resetMocks() {
        Mockito.reset(dataScriptDao);
    }

    @Test
    public void queryPredictionsByGroovyScriptTest() throws IOException {
        Mockito.when(tairClient.batchGetWithCache(Mockito.anyString(), Mockito.anyList(), Mockito.anyString())).thenReturn(ImmutableMap.of("1", "{\"rec\":\"val1\"}", "2", "{\"rec\":\"val2\"}"));
        Mockito.when(horizonStrategyService.getStrategyByLayerId(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn("stgy_v0");
        PredictionQueryContext context = PredictionQueryContext.newInstance("bizCode", Arrays.asList("1", "2", "3"), ImmutableMap.of("uuid", "uuid"));
        String script = String.join("\n", IOUtils.readLines(this.getClass().getResourceAsStream("/PredictionTest.groovy"), Charset.defaultCharset()));
        predictionQueryAppService.queryPredictionsByGroovyScript(script, context);
        Assert.assertEquals("stgy_v0", context.getRespExtra().get("stgy"));
        Assert.assertNotNull(context.getData().get("1"));
        Assert.assertEquals("val1", context.getData().get("1").get(0).getValues().get("rec"));
        Assert.assertNotNull(context.getData().get("2"));
        Assert.assertEquals("val2", context.getData().get("2").get(0).getValues().get("rec"));
    }

    /**
     * 测试queryScript方法，当bizCodes非空但返回空列表时
     */
    @Test
    public void testQueryScriptWithNonEmptyBizCodesReturnsEmpty() {
        // arrange
        List<String> bizCodes = Arrays.asList("code1", "code2");
        when(dataScriptDao.getValidByCode(bizCodes)).thenReturn(Arrays.asList());
        // act
        Map<String, String> result = predictionQueryAppService.queryScript(bizCodes);
        // assert
        Assert.assertTrue("结果应为空", result.isEmpty());
        verify(dataScriptDao).getValidByCode(bizCodes);
    }
}
