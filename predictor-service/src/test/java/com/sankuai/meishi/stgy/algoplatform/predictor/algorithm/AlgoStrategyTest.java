package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.InjectMocks;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.anyMap;
import org.mockito.MockitoAnnotations;
import org.mockito.Matchers;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

@RunWith(MockitoJUnitRunner.class)
public class AlgoStrategyTest {

    @Mock
    private AlgoPackage algoPackageMock;

    @InjectMocks
    private AlgoStrategy algoStrategy;

    private Long id = 1L;

    private String entrancePath = "path";

    private String entranceMethod = "method";

    private Map<String, Object> request;

    private Map<String, Object> expectedResponse;

    @Before
    public void setUp() {
        algoStrategy = new AlgoStrategy(id, algoPackageMock, entrancePath, entranceMethod);
        request = new HashMap<>();
        expectedResponse = new HashMap<>();
    }

    /**
     * 测试 run 方法异常情况
     */
    @Test(expected = Exception.class)
    public void testRunExceptionCase() throws Throwable {
        // arrange
        when(algoPackageMock.invoke(eq(entrancePath), eq(entranceMethod), anyMap(), null)).thenThrow(new Exception("invoke exception"));
        // act
        algoStrategy.run(request, null);
        // assert
        // Exception is expected to be thrown
    }
}
