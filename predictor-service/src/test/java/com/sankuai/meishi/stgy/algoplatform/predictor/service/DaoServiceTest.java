package com.sankuai.meishi.stgy.algoplatform.predictor.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meishi.stgy.algoplatform.predictor.BaseTest;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.AlgoPackageDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.AlgoStrategyDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.BizStrategyDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.PredictionDataScriptDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoPackagePo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoStrategyPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.BizStrategyPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.PredictionDataScriptPo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

@Slf4j
public class DaoServiceTest {

//    @Resource
    private BizStrategyDao bizStrategyDao;

//    @Resource
    private AlgoStrategyDao algoStrategyDao;

//    @Resource
    private AlgoPackageDao algoPackageDao;

//    @Resource
    private PredictionDataScriptDao predictionDataScriptDao;

//    @Test
    public void testBizStrategyDao() {
        String bizCode = "cookerygod_similarityDeal";
        BizStrategyPo bizStrategyPo = bizStrategyDao.getValidByCodeWithCache(bizCode);
        log.info("bizStrategyPo : {}", JSON.toJSONString(bizStrategyPo));
        Assert.assertNotNull("bizStrategy test data is null", bizStrategyPo);
    }

//    @Test
    public void testAlgoStrategyDao() {
        Long id = 2L;
        AlgoStrategyPo algoStrategyPo = algoStrategyDao.getValidByIdWithCache(id);
        log.info("algoStrategyPo : {}", JSON.toJSONString(algoStrategyPo));
        Assert.assertNotNull("algoStrategy test data is null", algoStrategyPo);
    }

//    @Test
    public void testAlgoPackageDao() {
        Long id = 3L;
        AlgoPackagePo algoPackagePo = algoPackageDao.getById(id);
        log.info("algoPackagePo : {}", JSON.toJSONString(algoPackagePo));
        Assert.assertNotNull("algoPackage test data is null", algoPackagePo);
    }

//    @Test
    public void testPredictionDataScriptDao() {
        String bizCode = "best_selling_product_quality_inspection";
        PredictionDataScriptPo predictionDataScriptPo = predictionDataScriptDao.getValidByCodeWithCache(bizCode);
        log.info("predictionDataScriptPo : {}", JSON.toJSONString(predictionDataScriptPo));
        Assert.assertNotNull("predictionDataScript test data is null", predictionDataScriptPo);
    }
}
