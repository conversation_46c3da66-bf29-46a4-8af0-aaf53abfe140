package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import java.lang.reflect.Field;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

@RunWith(MockitoJUnitRunner.class)
public class SpringContextUtils_IsSingletonTest {

    @Mock
    private ApplicationContext mockApplicationContext;

    @Before
    public void setUp() throws Exception {
        // 使用反射设置 applicationContext 静态成员变量
        Field applicationContextField = SpringContextUtils.class.getDeclaredField("applicationContext");
        applicationContextField.setAccessible(true);
        applicationContextField.set(null, mockApplicationContext);
    }

    /**
     * 测试 applicationContext 为 null 时抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testIsSingletonWhenApplicationContextIsNull() throws Throwable {
        // 使用反射将 applicationContext 设置为 null 来模拟这个场景
        Field applicationContextField = SpringContextUtils.class.getDeclaredField("applicationContext");
        applicationContextField.setAccessible(true);
        applicationContextField.set(null, null);
        SpringContextUtils.isSingleton("beanName");
    }

    /**
     * 测试存在给定名称的单例 bean
     */
    @Test
    public void testIsSingletonWithExistingSingletonBean() throws Throwable {
        // arrange
        String beanName = "singletonBean";
        when(mockApplicationContext.isSingleton(beanName)).thenReturn(true);
        // act
        boolean result = SpringContextUtils.isSingleton(beanName);
        // assert
        assertTrue("Bean should be singleton", result);
    }

    /**
     * 测试存在给定名称的非单例 bean
     */
    @Test
    public void testIsSingletonWithExistingNonSingletonBean() throws Throwable {
        // arrange
        String beanName = "nonSingletonBean";
        when(mockApplicationContext.isSingleton(beanName)).thenReturn(false);
        // act
        boolean result = SpringContextUtils.isSingleton(beanName);
        // assert
        assertFalse("Bean should not be singleton", result);
    }

    /**
     * 测试不存在给定名称的 bean 抛出 NoSuchBeanDefinitionException
     */
    @Test(expected = NoSuchBeanDefinitionException.class)
    public void testIsSingletonWithNonExistingBean() throws Throwable {
        // arrange
        String beanName = "nonExistingBean";
        when(mockApplicationContext.isSingleton(beanName)).thenThrow(new NoSuchBeanDefinitionException(beanName));
        // act
        SpringContextUtils.isSingleton(beanName);
    }
}
