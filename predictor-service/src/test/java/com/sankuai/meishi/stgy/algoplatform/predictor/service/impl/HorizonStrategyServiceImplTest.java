package com.sankuai.meishi.stgy.algoplatform.predictor.service.impl;

import com.sankuai.meishi.stgy.algoplatform.predictor.service.HorizonStrategyService;
import com.sankuai.service.wpt.horizon.core.IHorizonClient;
import com.sankuai.service.wpt.horizon.core.enums.HorizonExceptionEnum;
import com.sankuai.service.wpt.horizon.core.expection.HorizonException;
import com.sankuai.service.wpt.horizon.core.vo.HorizonStrategy;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

/**
 * 测试 HorizonStrategyServiceImpl.getStrategyByLayerId 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class HorizonStrategyServiceImplTest {

    @Mock
    private IHorizonClient horizonClient;

    @InjectMocks
    private HorizonStrategyServiceImpl service;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // ... 保留其他测试方法不变 ...
    // 其他测试方法代码保持不变
    /**
     * 测试正常场景，策略列表非空且存在匹配的策略
     */
    @Test
    public void testGetStrategyByLayerId_Normal() throws Throwable {
        // arrange
        String expKey = "expKey";
        String abKey = "abKey";
        int layerId = 1;
        List<HorizonStrategy> strategies = new ArrayList<>();
        HorizonStrategy strategy = new HorizonStrategy();
        strategy.setExpKey(expKey);
        strategy.setStrategyKey("strategyKey");
        strategies.add(strategy);
        when(horizonClient.getStrategyByLayerIdV2(eq(abKey), anyMap(), eq("group"), eq(layerId))).thenReturn(strategies);
        // act
        String result = service.getStrategyByLayerId(expKey, abKey, layerId);
        // assert
        assertEquals("strategyKey", result);
    }

    /**
     * 测试策略列表为空的场景
     */
    @Test
    public void testGetStrategyByLayerId_EmptyList() throws Throwable {
        // arrange
        String expKey = "expKey";
        String abKey = "abKey";
        int layerId = 1;
        when(horizonClient.getStrategyByLayerIdV2(eq(abKey), anyMap(), eq("group"), eq(layerId))).thenReturn(new ArrayList<>());
        // act
        String result = service.getStrategyByLayerId(expKey, abKey, layerId);
        // assert
        assertEquals("default", result);
    }

    /**
     * 测试未找到匹配策略的场景
     */
    @Test
    public void testGetStrategyByLayerId_NoMatch() throws Throwable {
        // arrange
        String expKey = "expKey";
        String abKey = "abKey";
        int layerId = 1;
        List<HorizonStrategy> strategies = new ArrayList<>();
        HorizonStrategy strategy = new HorizonStrategy();
        strategy.setExpKey("otherExpKey");
        strategy.setStrategyKey("strategyKey");
        strategies.add(strategy);
        when(horizonClient.getStrategyByLayerIdV2(eq(abKey), anyMap(), eq("group"), eq(layerId))).thenReturn(strategies);
        // act
        String result = service.getStrategyByLayerId(expKey, abKey, layerId);
        // assert
        assertEquals("default", result);
    }

    /**
     * 测试找到匹配策略但策略的 strategyKey 为 null 的场景
     */
    @Test
    public void testGetStrategyByLayerId_NullStrategyKey() throws Throwable {
        // arrange
        String expKey = "expKey";
        String abKey = "abKey";
        int layerId = 1;
        List<HorizonStrategy> strategies = new ArrayList<>();
        HorizonStrategy strategy = new HorizonStrategy();
        strategy.setExpKey(expKey);
        strategy.setStrategyKey(null);
        strategies.add(strategy);
        when(horizonClient.getStrategyByLayerIdV2(eq(abKey), anyMap(), eq("group"), eq(layerId))).thenReturn(strategies);
        // act
        String result = service.getStrategyByLayerId(expKey, abKey, layerId);
        // assert
        assertEquals("default", result);
    }
}
