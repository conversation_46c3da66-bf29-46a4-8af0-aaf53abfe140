package com.sankuai.meishi.stgy.algoplatform.predictor.mq;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meishi.stgy.algoplatform.predictor.TestCaseHelper;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.s3.AmazonS3Service;
import com.sankuai.meishi.stgy.algoplatform.predictor.thrift.TPredictServicePublish;
import com.sankuai.wenchang.infer.service.entity.AlgoProducerPack;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class DzPredictListenerTest {

    @InjectMocks
    private DzPredictListener dzPredictListener;

    @Mock
    private AmazonS3Service amazonS3Service;

    @Mock
    private TPredictServicePublish tPredictServicePublish;

    private final String compressStr = "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";
    /**
     * Tests receive method when an exception is expected during processing.
     */
    @Test
    public void testReceiveWhenExceptionOccurs() throws Throwable {
        MafkaMessage<String> invalidMsgBody = new MafkaMessage<>("", 1, 0L, "", "invalidMsgBody");
        // Given
        String msgBody = "invalidMsgBody";
        // When
        ConsumeStatus result = dzPredictListener.recvMessage(invalidMsgBody, null);
        // Then
        verify(tPredictServicePublish, never()).dzPredictWithPreprocess(any(TPredictRequest.class));
        assertEquals(ConsumeStatus.RECONSUME_LATER, result);
    }

    @Test
    public void parseMessageTest() throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        DzPredictListener dzPredictListener = new DzPredictListener();
        Method parseMessage = TestCaseHelper.getPrivateMethodForTest(DzPredictListener.class, "parseMessage", new Class[]{String.class});
        AlgoProducerPack invoke = (AlgoProducerPack) parseMessage.invoke(dzPredictListener, new Object[]{compressStr});
        Assert.assertNotNull(invoke);
    }
}
