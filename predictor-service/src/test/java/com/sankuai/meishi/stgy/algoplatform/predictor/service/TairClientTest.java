package com.sankuai.meishi.stgy.algoplatform.predictor.service;

import com.taobao.tair3.client.Result;
import com.taobao.tair3.client.ResultMap;
import com.taobao.tair3.client.error.TairFlowLimit;
import com.taobao.tair3.client.error.TairRpcError;
import com.taobao.tair3.client.error.TairTimeout;
import com.taobao.tair3.client.impl.MultiTairClient;
import com.taobao.tair3.client.util.ByteArray;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class TairClientTest {
    @Mock
    private MultiTairClient tairClient;
    @InjectMocks
    private TairClient tairTestClient;

    @Before
    public void before() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void batchGetTest() throws TairTimeout, InterruptedException, TairFlowLimit, TairRpcError {
        Assert.assertEquals(Collections.emptyMap(), tairTestClient.batchGet("", new ArrayList<>()));

        ResultMap<ByteArray, Result<byte[]>> mockResultMap = new ResultMap<>();
        mockResultMap.setResultCode(Collections.singleton(Result.ResultCode.OK));
        List<String> list = Arrays.asList("100", "101", "102");
        list.forEach(f -> {
            Result<byte[]> re = new Result<>();
            re.setCode(Result.ResultCode.OK);
            re.setKey(f.getBytes());
            re.setResult(f.getBytes());
            mockResultMap.put(new ByteArray(f.getBytes()), re);
        });
        Mockito.when(tairClient.batchGet(Mockito.anyShort(), Mockito.anyList(), Mockito.any())).thenReturn(mockResultMap);
        Map<String, String> res = tairTestClient.batchGet("", list);
        Assert.assertEquals(list.stream().collect(Collectors.toMap(Function.identity(), Function.identity())), res);
    }

}
