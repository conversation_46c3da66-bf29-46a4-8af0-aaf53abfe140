package com.sankuai.meishi.stgy.algoplatform.predictor.util;


import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;

public class BmlDataReportUtilTest {

    /**
     * 测试 reportData 方法，当 pReportMessage 为 null 时
     */
    @Test
    public void testReportDataWhenPReportMessageIsNull() {
        PReportMessage pReportMessage = null;
        BmlDataReportUtil.reportData(pReportMessage);
        assert pReportMessage == null;
    }

    @Test
    public void testReportData() {
        String info = "{\n" +
                "\t\"buEnum\": \"DAOCAN\",\n" +
                "\t\"businessId\": \"acfd6b6b9fe944fd879381ae3fa3cd3e\",\n" +
                "\t\"customData\": {\n" +
                "\t\t\"bizCode\": \"zb_sameGoodsMatch\",\n" +
                "\t\t\"dj_poi_id\": \"n0+dfDpMGF4FZ5V7ZX/WOiMXur2/s+d1tmkZuNt82Fc=\",\n" +
                "\t\t\"dp_brand_id\": \"581583\"\n" +
                "\t},\n" +
                "\t\"djPoiId\": \"n0+dfDpMGF4FZ5V7ZX/WOiMXur2/s+d1tmkZuNt82Fc=\",\n" +
                "\t\"eventDesc\": \"匹配结果\",\n" +
                "\t\"resp\": \"{\\\"code\\\":0,\\\"data\\\":{\\\"dj_full_match\\\":\\\"[]\\\",\\\"code\\\":\\\"0\\\",\\\"match\\\":\\\"\\\",\\\"compressed_match\\\":\\\"H4sIAAAAAAAAAN1UXU/aUBj+L72lsdCCgMkuTKSa8LFsMwNZlqa0RTrox0phumWJLGEyNwMzxkiEGHWMzEXMYsYQkP0Y+8G58i/sQJ3ckCzuI2676ul5Tt/nfZ63z3nwDBGYBMXy6QTFi3EpjUwhZu4j2MyD1h6utdta5+yiWwD5T+B9GdN7jX7vAAPtDnxcdF+h5u6JflQCta7WWgflElgtov3VQ33tg9Fs9IsVrdWC8AgD1X2jumdsnWq9r3A9AszcZ3D0or/dMrbKkK6/UTFOajZzswJevh7w6MVj7bSEgp1Dc7+OoAgvqzfes9EsGMcb4F1Jz3/5sYBcTV/b0RvVsVIEWklyKiUrPMMhUw7cOWG3AFmR2AyjUiItQADRi2+gPLO+rReaV4pthF07K5v19e/9d8DB7lWb5ysla9/SeL7yFnIORv6HSg8nw9Epimdh1RQWvsvOBUl/JBlVJx/7lueDApb1ydy9+dhsdMFDE1KcEUO2kHf6Fvw4zUgKbMY+4fXgTruLmHS6HAThdeMokknTixylcGkVmqTykjiY+qWUEZ/fk5hm3JlkTFACvgWMlCLumSzpeBJQhaxv7k4q5A8HhKePllyLzJBPVaD36tANjoV1ofmwpMRyqct9dVkeuEPAw7LEWySi3cbGZ+TgLOkko6777mgEC9/mg5GMgmNpG+tQhWQ0E1I9OGmRKBLDjaOB72MHz8pUTKFF1mJDnqN/R0Z/4/9+01J+5rr5bzN63cz8akaveyf8Exl9+A2fBuSbSQcAAA==\\\"},\\\"setData\\\":true,\\\"extra\\\":{\\\"cost\\\":\\\"8\\\",\\\"cacheKey\\\":\\\"zb_sameGoodsMatch_4_60_fc3da7b38ff33acb1d56b9b51fe0d5a8_staging\\\",\\\"cacheVersion\\\":\\\"c8218fc514c\\\",\\\"useCache\\\":\\\"true\\\",\\\"unique_key\\\":\\\"fc3da7b38ff33acb1d56b9b51fe0d5a8\\\",\\\"businessId\\\":\\\"acfd6b6b9fe944fd879381ae3fa3cd3e\\\",\\\"strategy\\\":\\\"deal_match\\\",\\\"distribution\\\":\\\"group_a\\\",\\\"scene\\\":\\\"1\\\"},\\\"extraSize\\\":9,\\\"setCode\\\":true,\\\"dataSize\\\":4,\\\"partition_date\\\":\\\"2024-11-18 14:28:14\\\",\\\"match_time\\\":\\\"2024-11-18 14:28:14\\\",\\\"match_time_str\\\":\\\"2024-11-18 14:28:14\\\",\\\"setMessage\\\":false,\\\"setExtra\\\":true}\",\n" +
                "\t\"success\": true,\n" +
                "\t\"traceId\": \"MATCH1731911294190191346\"\n" +
                "}\n";
        PReportMessage pReportMessage = JSONObject.parseObject(info, PReportMessage.class);
        BmlDataReportUtil.reportData(pReportMessage);
        assert info != null;
    }

    @Test
    public void testReportDataException() {
        String info = "{\n" +
                "\t\"buEnum\": \"DAOCAN\",\n" +
                "\t\"businessId\": \"acfd6b6b9fe944fd879381ae3fa3cd3e\",\n" +
                "\t\"customData\": {\n" +
                "\t\t\"bizCode\": \"zb_sameGoodsMatch\",\n" +
                "\t\t\"dj_poi_id\": \"n0+dfDpMGF4FZ5V7ZX/WOiMXur2/s+d1tmkZuNt82Fc=\",\n" +
                "\t\t\"dp_brand_id\": \"581583\"\n" +
                "\t},\n" +
                "\t\"djPoiId\": \"n0+dfDpMGF4FZ5V7ZX/WOiMXur2/s+d1tmkZuNt82Fc=\",\n" +
                "\t\"eventDesc\": \"匹配结果\",\n" +
                "\t\"resp\": \"{\\\"code\\\":0,\\\"data\\\":{\\\"dj_full_match\\\":\\\"[]\\\",\\\"code\\\":\\\"0\\\",\\\"match\\\":\\\"\\\",\\\"compressed_match\\\":\\\"H4sIAAAAAAAAAN1UXU/aUBj+L72lsdCCgMkuTKSa8LFsMwNZlqa0RTrox0phumWJLGEyNwMzxkiEGHWMzEXMYsYQkP0Y+8G58i/sQJ3ckCzuI2676ul5Tt/nfZ63z3nwDBGYBMXy6QTFi3EpjUwhZu4j2MyD1h6utdta5+yiWwD5T+B9GdN7jX7vAAPtDnxcdF+h5u6JflQCta7WWgflElgtov3VQ33tg9Fs9IsVrdWC8AgD1X2jumdsnWq9r3A9AszcZ3D0or/dMrbKkK6/UTFOajZzswJevh7w6MVj7bSEgp1Dc7+OoAgvqzfes9EsGMcb4F1Jz3/5sYBcTV/b0RvVsVIEWklyKiUrPMMhUw7cOWG3AFmR2AyjUiItQADRi2+gPLO+rReaV4pthF07K5v19e/9d8DB7lWb5ysla9/SeL7yFnIORv6HSg8nw9Epimdh1RQWvsvOBUl/JBlVJx/7lueDApb1ydy9+dhsdMFDE1KcEUO2kHf6Fvw4zUgKbMY+4fXgTruLmHS6HAThdeMokknTixylcGkVmqTykjiY+qWUEZ/fk5hm3JlkTFACvgWMlCLumSzpeBJQhaxv7k4q5A8HhKePllyLzJBPVaD36tANjoV1ofmwpMRyqct9dVkeuEPAw7LEWySi3cbGZ+TgLOkko6777mgEC9/mg5GMgmNpG+tQhWQ0E1I9OGmRKBLDjaOB72MHz8pUTKFF1mJDnqN/R0Z/4/9+01J+5rr5bzN63cz8akaveyf8Exl9+A2fBuSbSQcAAA==\\\"},\\\"setData\\\":true,\\\"extra\\\":{\\\"cost\\\":\\\"8\\\",\\\"cacheKey\\\":\\\"zb_sameGoodsMatch_4_60_fc3da7b38ff33acb1d56b9b51fe0d5a8_staging\\\",\\\"cacheVersion\\\":\\\"c8218fc514c\\\",\\\"useCache\\\":\\\"true\\\",\\\"unique_key\\\":\\\"fc3da7b38ff33acb1d56b9b51fe0d5a8\\\",\\\"businessId\\\":\\\"acfd6b6b9fe944fd879381ae3fa3cd3e\\\",\\\"strategy\\\":\\\"deal_match\\\",\\\"distribution\\\":\\\"group_a\\\",\\\"scene\\\":\\\"1\\\"},\\\"extraSize\\\":9,\\\"setCode\\\":true,\\\"dataSize\\\":4,\\\"partition_date\\\":\\\"2024-11-18 14:28:14\\\",\\\"match_time\\\":\\\"2024-11-18 14:28:14\\\",\\\"match_time_str\\\":\\\"2024-11-18 14:28:14\\\",\\\"setMessage\\\":false,\\\"setExtra\\\":true}\",\n" +
                "\t\"success\": true,\n" +
                "\t\"traceId\": \"MATCH1731911294190191346\"\n" +
                "\n";
        try {
            PReportMessage pReportMessage = JSONObject.parseObject(info, PReportMessage.class);
            BmlDataReportUtil.reportData(pReportMessage);
        } catch (Exception e) {
            assert e instanceof JSONException;
        }
    }
}
