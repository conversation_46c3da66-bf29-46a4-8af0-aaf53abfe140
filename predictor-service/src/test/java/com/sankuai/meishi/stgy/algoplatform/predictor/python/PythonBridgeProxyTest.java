package com.sankuai.meishi.stgy.algoplatform.predictor.python;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import py4j.GatewayServer;
import java.lang.reflect.Field;
import static org.junit.Assert.*;
import java.lang.reflect.Method;
import static org.mockito.Mockito.*;
import java.util.concurrent.ExecutionException;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;

/**
 * 测试PythonBridgeProxy类中getProxyInstance方法的所有场景
 */
public class PythonBridgeProxyTest {

    /**
     * 重置静态字段到默认状态
     */
    @After
    public void resetStaticFields() throws Exception {
        // 直接获取proxyCache并调用invalidateAll方法来清除缓存
        Field proxyCacheField = PythonBridgeProxy.class.getDeclaredField("proxyCache");
        proxyCacheField.setAccessible(true);
        Cache<String, PythonBridge> proxyCache = (Cache<String, PythonBridge>) proxyCacheField.get(null);
        proxyCache.invalidateAll();
    }

    /**
     * 测试getProxyInstance方法正常情况
     */
    @Test
    public void testGetProxyInstanceNormal() throws Throwable {
        // arrange
        String id = "testId";
        GatewayServer server = new GatewayServer();
        // act
        PythonBridge result = PythonBridgeProxy.getProxyInstance(id, server);
        // assert
        assertNotNull(result);
    }
}
