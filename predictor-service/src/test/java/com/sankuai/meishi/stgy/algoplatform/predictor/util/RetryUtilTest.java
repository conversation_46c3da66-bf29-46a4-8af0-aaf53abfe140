package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import java.util.function.Function;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith;
import static org.mockito.Mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * RetryUtil测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class RetryUtilTest {

    private RetryUtil.Runnable runnable;

    private Function<Exception, Boolean> getNeedRetry;

    @Before
    public void setUp() {
        runnable = mock(RetryUtil.Runnable.class);
        getNeedRetry = mock(Function.class);
    }

    // ... 保持其他测试方法不变 ...
    /**
     * 测试maxAttempts为0时的行为
     */
//    @Test
//    public void testRetryWithZeroMaxAttempts() throws Exception {
//        // arrange
//        doThrow(new RuntimeException("Test Exception")).when(runnable).run();
//        when(getNeedRetry.apply(any(Exception.class))).thenReturn(true);
//        // act
//        RetryUtil.retry(runnable, getNeedRetry, 0, 100L);
//        // assert
//        verify(runnable, never()).run();
//        verify(getNeedRetry, never()).apply(any(Exception.class));
//    }

    /**
     * 测试runnable执行异常，但不需要重试
     */
    @Test
    public void testRetryExceptionNoRetry() throws Exception {
        // arrange
        doThrow(new RuntimeException("Test Exception")).when(runnable).run();
        when(getNeedRetry.apply(any(Exception.class))).thenReturn(false);
        // act
        try {
            RetryUtil.retry(runnable, getNeedRetry, 3, 100L);
        } catch (RuntimeException e) {
            // assert
            assertEquals("Test Exception", e.getCause().getMessage());
        }
        verify(runnable, times(1)).run();
        verify(getNeedRetry, times(1)).apply(any(Exception.class));
    }

    /**
     * 测试runnable执行异常，需要重试，但最终成功
     */
    @Test
    public void testRetryExceptionWithSuccessfulRetry() throws Exception {
        // arrange
        doThrow(new RuntimeException("Test Exception")).doNothing().when(runnable).run();
        when(getNeedRetry.apply(any(Exception.class))).thenReturn(true);
        // 处理null值
        when(getNeedRetry.apply(null)).thenReturn(false);
        // act
        RetryUtil.retry(runnable, getNeedRetry, 3, 100L);
        // assert
        verify(runnable, times(2)).run();
        verify(getNeedRetry, times(1)).apply(any(Exception.class));
    }

    // ... 保持其他测试方法不变 ...
    /**
     * 测试runnable正常执行，不需要重试
     */
    @Test
    public void testRetryNoNeedToRetry() throws Exception {
        // arrange
        when(getNeedRetry.apply(null)).thenReturn(false);
        // act
        RetryUtil.retry(runnable, getNeedRetry, 3, 100L);
        // assert
        verify(runnable, times(1)).run();
        verify(getNeedRetry, never()).apply(any(Exception.class));
    }

    // ... 保持其他测试方法不变 ...
    // 其他测试方法保持不变
    /**
     * 测试runnable执行异常，需要重试，中间抛出InterruptedException
     */
    @Test(expected = RuntimeException.class)
    public void testRetryExceptionWithInterruptedException() throws Exception {
        // arrange
        doThrow(new InterruptedException("Test InterruptedException")).when(runnable).run();
        when(getNeedRetry.apply(any(Exception.class))).thenReturn(true);
        // act
        RetryUtil.retry(runnable, getNeedRetry, 3, 100L);
        // assert
        verify(runnable, times(1)).run();
        verify(getNeedRetry, times(1)).apply(any(Exception.class));
    }

    /**
     * 测试runnable执行异常，需要重试，且重试次数达到上限
     */
    @Test(expected = RuntimeException.class)
    public void testRetryExceptionWithRetryLimitReached() throws Exception {
        // arrange
        doThrow(new Exception("Test Exception")).when(runnable).run();
        when(getNeedRetry.apply(any(Exception.class))).thenReturn(true);
        // act
        RetryUtil.retry(runnable, getNeedRetry, 3, 100);
        // assert
        verify(runnable, times(3)).run();
        verify(getNeedRetry, times(3)).apply(any(Exception.class));
    }

    /**
     * 测试runnable执行异常，需要重试，且每次重试都失败
     */
    @Test(expected = RuntimeException.class)
    public void testRetryExceptionWithAllRetriesFailed() throws Exception {
        // arrange
        doThrow(new Exception("Test Exception")).when(runnable).run();
        when(getNeedRetry.apply(any(Exception.class))).thenReturn(true);
        // act
        RetryUtil.retry(runnable, getNeedRetry, 3, 100);
        // assert
        verify(runnable, times(3)).run();
        verify(getNeedRetry, times(3)).apply(any(Exception.class));
    }
}
