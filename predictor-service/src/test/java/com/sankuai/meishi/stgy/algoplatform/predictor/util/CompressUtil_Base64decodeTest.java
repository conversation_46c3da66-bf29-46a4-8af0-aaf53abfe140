package com.sankuai.meishi.stgy.algoplatform.predictor.util;

import java.nio.charset.StandardCharsets;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

public class CompressUtil_Base64decodeTest {

    /**
     * 测试 base64decode 方法，输入有效的 Base64 编码字符串
     */
    @Test
    public void testBase64decodeValidInput() {
        // arrange
        // "Hello World" in Base64
        String validBase64String = "SGVsbG8gV29ybGQ=";
        byte[] expectedOutput = "Hello World".getBytes(StandardCharsets.UTF_8);
        // act
        byte[] actualOutput = CompressUtil.base64decode(validBase64String);
        // assert
        assertArrayEquals(expectedOutput, actualOutput);
    }

    /**
     * 测试 base64decode 方法，输入无效的 Base64 编码字符串
     */
    @Test(expected = IllegalArgumentException.class)
    public void testBase64decodeInvalidInput() {
        // arrange
        String invalidBase64String = "Invalid Base64";
        // act
        CompressUtil.base64decode(invalidBase64String);
        // assert is handled by the expected exception
    }

    /**
     * 测试 base64decode 方法，输入为空字符串
     */
    @Test
    public void testBase64decodeEmptyString() {
        // arrange
        String emptyString = "";
        byte[] expectedOutput = new byte[0];
        // act
        byte[] actualOutput = CompressUtil.base64decode(emptyString);
        // assert
        assertArrayEquals(expectedOutput, actualOutput);
    }

    /**
     * 测试 base64decode 方法，输入为 null
     */
    @Test(expected = NullPointerException.class)
    public void testBase64decodeNullInput() {
        // arrange
        String nullString = null;
        // act
        CompressUtil.base64decode(nullString);
        // assert is handled by the expected exception
    }
}
