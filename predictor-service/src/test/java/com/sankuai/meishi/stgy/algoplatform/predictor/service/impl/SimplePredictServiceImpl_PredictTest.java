package com.sankuai.meishi.stgy.algoplatform.predictor.service.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.meituan.hadoop.afo.serving.client.thrift.MTThriftPredictClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.SimplePredictService;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.util.TensorUtil;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.tensorflow.framework.TensorProto;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import org.tensorflow.framework.TensorShapeProto;
import com.google.common.collect.Maps;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks.*;
import org.mockito.Mock.*;

@RunWith(MockitoJUnitRunner.class)
public class SimplePredictServiceImpl_PredictTest {

    @Mock
    private MTThriftPredictClient mockClient;

    private SimplePredictService simplePredictService;

    @Before
    public void setUp() throws Exception {
        simplePredictService = new SimplePredictServiceImpl();
        LoadingCache<String, MTThriftPredictClient> cache = CacheBuilder.newBuilder().build(new CacheLoader<String, MTThriftPredictClient>() {

            @Override
            public MTThriftPredictClient load(String key) {
                return mockClient;
            }
        });
        Field predictClientCacheField = SimplePredictServiceImpl.class.getDeclaredField("predictClientCache");
        predictClientCacheField.setAccessible(true);
        predictClientCacheField.set(simplePredictService, cache);
    }

    @Test
    public void testPredictWithValidFeatureAndResponse() throws Exception {
        String modelName = "testModel";
        Long modelVersion = 1L;
        String signatureName = "testSignature";
        Class<Integer> clazz = Integer.class;
        Map<String, List<List<Integer>>> feature = new HashMap<>();
        feature.put("testFeature", Collections.singletonList(Collections.singletonList(1)));
        int dim = 2;
        Map<String, TensorProto> clientResponse = new HashMap<>();
        TensorProto tensorProto = TensorUtil.buildIntTensor(Collections.singletonList(1), 1, 1);
        clientResponse.put("testFeature", tensorProto);
        when(mockClient.predict(any(Map.class))).thenReturn(clientResponse);
        Map<String, List<?>> result = simplePredictService.predict(modelName, modelVersion, signatureName, clazz, feature, dim);
        assertNotNull("结果不应为空", result);
        assertEquals("结果大小应为 1", 1, result.size());
        assertTrue("结果应包含 testFeature 键", result.containsKey("testFeature"));
        assertNotNull("testFeature 的值不应为空", result.get("testFeature"));
        assertEquals("testFeature 的值应为 [[1]]", Collections.singletonList(Collections.singletonList(1)), result.get("testFeature"));
    }

    @Test
    public void testPredictWithEmptyFeature() {
        String modelName = "testModel";
        Long modelVersion = 1L;
        String signatureName = "testSignature";
        Class<Integer> clazz = Integer.class;
        Map<String, List<List<Integer>>> feature = Collections.emptyMap();
        int dim = 2;
        Map<String, List<?>> result = simplePredictService.predict(modelName, modelVersion, signatureName, clazz, feature, dim);
        assertTrue("结果应为空", result.isEmpty());
    }

    @Test(expected = RuntimeException.class)
    public void testPredictWithExceptionFromClient() throws Exception {
        String modelName = "testModel";
        Long modelVersion = 1L;
        String signatureName = "testSignature";
        Class<Integer> clazz = Integer.class;
        Map<String, List<List<Integer>>> feature = new HashMap<>();
        feature.put("testFeature", Collections.singletonList(Collections.singletonList(1)));
        int dim = 2;
        when(mockClient.predict(any(Map.class))).thenThrow(new RuntimeException("Client exception"));
        simplePredictService.predict(modelName, modelVersion, signatureName, clazz, feature, dim);
    }
}
