package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.impl;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackage;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoPackageService;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.AlgoStrategy;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictContext;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.cache.PredictCacheConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.Constants;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.AlgoStrategyDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.daoservice.BizStrategyDao;
import com.sankuai.meishi.stgy.algoplatform.predictor.dal.entity.AlgoStrategyPo;
import com.sankuai.meishi.stgy.algoplatform.predictor.service.TairClient;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PredictAppServiceImplTest {

    @Mock
    private BizStrategyDao mockBizStrategyDao;

    @Mock
    private AlgoStrategyDao mockAlgoStrategyDao;

    @Mock
    private AlgoPackageService mockAlgoPackageService;

    @InjectMocks
    private PredictAppServiceImpl predictAppServiceImplUnderTest;

    @Mock
    private TairClient tairClient;

    private Map<String, ?> req = new HashMap<>();

    private Map<String, ?> respMap = new HashMap<>();

    private Long strategyId = 1L;

    private PredictCacheConfig cacheConfig = new PredictCacheConfig();

    private PredictAppServiceImpl predictAppService = new PredictAppServiceImpl();

    public PredictAppServiceImplTest() {
        MockitoAnnotations.initMocks(this);
    }

    @After
    public void resetMocks() {
        Mockito.reset(mockAlgoStrategyDao, mockAlgoPackageService);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(PredictAppServiceImpl.class);
    }

    private void invokeSaveSameParamCache(String cacheKey, Map<String, ?> respMap, Long strategyId, PredictCacheConfig cacheConfig, Map<String, ?> req) throws Exception {
        Method method = PredictAppServiceImpl.class.getDeclaredMethod("saveSameParamCache", String.class, Map.class, Long.class, PredictCacheConfig.class, Map.class);
        method.setAccessible(true);
        method.invoke(predictAppServiceImplUnderTest, cacheKey, respMap, strategyId, cacheConfig, req);
    }

    private String invokePrivateMethod(String methodName, Map<String, Object> req) throws Exception {
        Method method = PredictAppServiceImpl.class.getDeclaredMethod(methodName, Map.class);
        method.setAccessible(true);
        return (String) method.invoke(predictAppService, req);
    }

    private String invokePrivateMethod(Map<String, ?> req) throws Exception {
        Method method = PredictAppServiceImpl.class.getDeclaredMethod("getMtSecondCateName", Map.class);
        method.setAccessible(true);
        return (String) method.invoke(predictAppService, req);
    }

    @Test
    public void testPredict_BizStrategyDaoReturnsNull() {
        // Setup
        final PredictContext context = new PredictContext();
        context.setBizCode("bizCode");
        context.setAbtestKey("abtestKey");
        context.setReq(new HashMap<>());
        context.setReqExtra(new HashMap<>());
        context.setResp(new HashMap<>());
        context.setRespExtra(new HashMap<>());
        context.setContextData(new HashMap<>());
        when(mockBizStrategyDao.getValidByCodeWithCache("bizCode")).thenReturn(null);
        // Run the test
        assertThatThrownBy(() -> predictAppServiceImplUnderTest.predict(context)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testGetStrategyById() {
        // Setup
        // Configure AlgoStrategyDao.getValidByIdWithCache(...).
        final AlgoStrategyPo algoStrategyPo = AlgoStrategyPo.builder().id(0L).packageId(0L).entrancePath("entrancePath").entranceMethod("entranceMethod").build();
        when(mockAlgoStrategyDao.getValidByIdWithCache(0L)).thenReturn(algoStrategyPo);
        // Configure AlgoPackageService.get(...).
        final AlgoPackage algoPackage = new AlgoPackage();
        algoPackage.setId(0L);
        algoPackage.setNote("note");
        algoPackage.setOwnerMis("ownerMis");
        algoPackage.setRuntime("runtime");
        algoPackage.setVersion("version");
        when(mockAlgoPackageService.get(0L)).thenReturn(algoPackage);
        // Run the test
        final AlgoStrategy result = predictAppServiceImplUnderTest.getStrategyById(0L);
        // Verify the results
    }

    /**
     * 测试getStrategyById方法，当AlgoStrategyDao返回有效的AlgoStrategyPo且AlgoPackageService返回有效的AlgoPackage时
     */
    @Test
    public void testGetStrategyById_ValidScenario() {
        // arrange
        AlgoStrategyPo algoStrategyPo = new AlgoStrategyPo();
        algoStrategyPo.setId(1L);
        algoStrategyPo.setPackageId(1L);
        algoStrategyPo.setEntrancePath("path");
        algoStrategyPo.setEntranceMethod("method");
        Mockito.when(mockAlgoStrategyDao.getValidByIdWithCache(1L)).thenReturn(algoStrategyPo);
        AlgoPackage algoPackage = new AlgoPackage(1L, "note", "owner", "runtime", "modulePath", "version", Constants.ALGO_PACKAGE_GIT_URL);
        Mockito.when(mockAlgoPackageService.get(1L)).thenReturn(algoPackage);
        // act
        AlgoStrategy result = predictAppServiceImplUnderTest.getStrategyById(1L);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(Long.valueOf(1L), result.getId());
        Assert.assertEquals("path", result.getEntrancePath());
        Assert.assertEquals("method", result.getEntranceMethod());
        Assert.assertEquals(algoPackage, result.getAlgoPackage());
    }

    @Test
    public void testSaveSameParamCacheCacheConfigIsNull() throws Throwable {
        invokeSaveSameParamCache("cacheKey", respMap, strategyId, null, req);
        verify(tairClient, never()).put(anyString(), anyString(), anyLong(), any());
    }

    @Test
    public void testSaveSameParamCacheCacheKeyIsEmpty() throws Throwable {
        invokeSaveSameParamCache("", respMap, strategyId, cacheConfig, req);
        verify(tairClient, never()).put(anyString(), anyString(), anyLong(), any());
    }

    @Test
    public void testSaveSameParamCacheMtPoiIdIsNotBlankAndModelHasFailed() throws Throwable {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("mt_deals", "[{\"mt_poi_id\":\"123\"}]");
        req = reqMap;
        when(tairClient.get(anyString())).thenReturn("errorLabel");
        invokeSaveSameParamCache("cacheKey", respMap, strategyId, cacheConfig, req);
        verify(tairClient, never()).put(anyString(), anyString(), anyLong(), any());
    }

    @Test
    public void testSaveSameParamCacheNormalCase() throws Throwable {
        when(mockAlgoStrategyDao.getValidByIdWithCache(strategyId)).thenReturn(new AlgoStrategyPo());
        invokeSaveSameParamCache("cacheKey", respMap, strategyId, cacheConfig, req);
        verify(tairClient, times(1)).put(anyString(), anyString(), anyLong(), any());
    }

    @Test
    public void testSaveSameParamCacheExceptionOccurred() throws Throwable {
        when(mockAlgoStrategyDao.getValidByIdWithCache(strategyId)).thenReturn(new AlgoStrategyPo());
        doThrow(new RuntimeException()).when(tairClient).put(anyString(), anyString(), anyLong(), any());
        invokeSaveSameParamCache("cacheKey", respMap, strategyId, cacheConfig, req);
        verify(tairClient, times(1)).put(anyString(), anyString(), anyLong(), any());
    }

    @Test
    public void testGetMtPoiIdFromReqNull() throws Throwable {
        try {
            Map<String, Object> req = null;
            invokePrivateMethod("getMtPoiIdFromReq", req);
            fail("Expected an InvocationTargetException wrapping a NullPointerException");
        } catch (InvocationTargetException e) {
            if (e.getCause() instanceof NullPointerException) {
                // Expected exception caught, test passes
                return;
            }
            fail("Expected an InvocationTargetException wrapping a NullPointerException, but got " + e.getCause().getClass().getName());
        } catch (Exception e) {
            fail("Expected an InvocationTargetException wrapping a NullPointerException, but got a different exception type: " + e.getClass().getName());
        }
    }

    @Test
    public void testGetMtPoiIdFromReqNoMtDeals() throws Throwable {
        Map<String, Object> req = new HashMap<>();
        String result = invokePrivateMethod("getMtPoiIdFromReq", req);
        assertNull(result);
    }

    @Test
    public void testGetMtPoiIdFromReqMtDealsNull() throws Throwable {
        Map<String, Object> req = new HashMap<>();
        req.put("mt_deals", null);
        String result = invokePrivateMethod("getMtPoiIdFromReq", req);
        assertNull(result);
    }

    @Test
    public void testGetMtPoiIdFromReqMtDealsEmpty() throws Throwable {
        Map<String, Object> req = new HashMap<>();
        req.put("mt_deals", "[]");
        String result = invokePrivateMethod("getMtPoiIdFromReq", req);
        assertNull(result);
    }

    @Test
    public void testGetMtPoiIdFromReqNoMtPoiId() throws Throwable {
        Map<String, Object> req = new HashMap<>();
        req.put("mt_deals", "[{\"other_key\":\"value\"}]");
        String result = invokePrivateMethod("getMtPoiIdFromReq", req);
        assertNull(result);
    }

    @Test
    public void testGetMtPoiIdFromReqWithMtPoiId() throws Throwable {
        Map<String, Object> req = new HashMap<>();
        req.put("mt_deals", "[{\"mt_poi_id\":\"123\"}]");
        String result = invokePrivateMethod("getMtPoiIdFromReq", req);
        assertEquals("123", result);
    }

    @Test(expected = Exception.class)
    public void testGetMtSecondCateNameWhenMapIsNull() throws Throwable {
        Map<String, Object> req = null;
        invokePrivateMethod(req);
    }

    @Test
    public void testGetMtSecondCateNameWhenMtDealsIsEmpty() throws Throwable {
        Map<String, Object> req = new HashMap<>();
        req.put("mt_deals", "");
        String result = invokePrivateMethod(req);
        assertEquals("", result);
    }

    @Test
    public void testGetMtSecondCateNameWhenMtDealsIsNotEmptyButJsonIsEmpty() throws Throwable {
        Map<String, Object> req = new HashMap<>();
        req.put("mt_deals", "[]");
        String result = invokePrivateMethod(req);
        assertEquals("", result);
    }

    @Test
    public void testGetMtSecondCateNameWhenMtDealsIsNotEmptyAndJsonIsNotEmptyButMtSecondCateNameIsEmpty() throws Throwable {
        Map<String, Object> req = new HashMap<>();
        req.put("mt_deals", "[{\"mt_second_cate_name\":\"\"}]");
        String result = invokePrivateMethod(req);
        assertEquals("", result);
    }

    @Test
    public void testGetMtSecondCateNameWhenMtDealsIsNotEmptyAndJsonIsNotEmptyAndMtSecondCateNameIsNotEmpty() throws Throwable {
        Map<String, Object> req = new HashMap<>();
        req.put("mt_deals", "[{\"mt_second_cate_name\":\"test\"}]");
        String result = invokePrivateMethod(req);
        assertEquals("test", result);
    }
}
