package com.sankuai.meishi.stgy.algoplatform.predictor.mq;

import static org.mockito.Mockito.*;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

public class DzPredictorResultProducerTest {

    @InjectMocks
    private DzPredictorResultProducer dzPredictorResultProducer;

    @Mock
    private IProducerProcessor producer;

    // Removed @Mock for Logger as we are not verifying logs
    public DzPredictorResultProducerTest() {
        // Initialize mocks in the constructor
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Tests the send method under normal conditions.
     */
    @Test
    public void testSendNormal() throws Throwable {
        // arrange
        String msg = "test message";
        ProducerResult producerResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producer.sendMessage(msg)).thenReturn(producerResult);
        // act
        dzPredictorResultProducer.send(msg);
        // assert
        verify(producer, times(1)).sendMessage(msg);
        // Removed verification of log.info as per the rules
    }

    /**
     * Tests the send method under exception conditions.
     */
    @Test(expected = Exception.class)
    public void testSendException() throws Throwable {
        // arrange
        String msg = "test message";
        when(producer.sendMessage(msg)).thenThrow(new Exception());
        // act
        dzPredictorResultProducer.send(msg);
        // assert
        verify(producer, times(1)).sendMessage(msg);
    }
}
