package com.sankuai.meishi.stgy.algoplatform.predictor.algorithm;

import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.IOException;

public class FileDelTest {

    @Test
    public void test1() throws IOException {
        String basePath = ResourceUtils.getURL("classpath:").getPath();
        System.out.println(basePath);

        File packageFile = new File(basePath + "/package");
        if (!packageFile.exists()) {
            packageFile.mkdir();
        }

        File abcFile = new File(packageFile.getAbsolutePath() + "/abc");
        if (!abcFile.exists()) {
            abcFile.mkdir();
        }
        assert abcFile.exists();

        File abc1TxtFile = new File(abcFile.getAbsolutePath() + "/1.txt");
        if (!abc1TxtFile.exists()) {
            abc1TxtFile.createNewFile();
        }
        assert abc1TxtFile.exists();

        File edfFile = new File(packageFile.getAbsolutePath() + "/edf");
        if (!edfFile.exists()) {
            edfFile.mkdir();
        }
        assert edfFile.exists();


        FileUtils.deleteDirectory(new File(abcFile.getAbsolutePath()));
        assert !abc1TxtFile.exists();
        assert !abcFile.exists();
        assert edfFile.exists();
        assert packageFile.exists();
        assert packageFile.listFiles() != null && packageFile.listFiles().length == 1 &&
                packageFile.listFiles()[0].getAbsolutePath().equals(edfFile.getAbsolutePath());

        FileUtils.deleteDirectory(new File(edfFile.getAbsolutePath()));
        assert !abcFile.exists();
        assert !edfFile.exists();
        assert packageFile.exists();

        assert packageFile.listFiles() == null || packageFile.listFiles().length == 0;
    }
}
