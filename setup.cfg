[metadata]
name = pandas
description = Powerful data structures for data analysis, time series, and statistics
long_description = file: README.md
long_description_content_type = text/markdown
url = https://pandas.pydata.org
author = The Pandas Development Team
author_email = <EMAIL>
license = BSD-3-Clause
license_file = LICENSE
platforms = any
classifiers =
    Development Status :: 5 - Production/Stable
    Environment :: Console
    Intended Audience :: Science/Research
    License :: OSI Approved :: BSD License
    Operating System :: OS Independent
    Programming Language :: Cython
    Programming Language :: Python
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3 :: Only
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Topic :: Scientific/Engineering
project_urls =
    Bug Tracker = https://github.com/pandas-dev/pandas/issues
    Documentation = https://pandas.pydata.org/pandas-docs/stable
    Source Code = https://github.com/pandas-dev/pandas

[options]
packages = find:
install_requires =
    numpy>=1.18.5; platform_machine!='aarch64' and platform_machine!='arm64' and python_version<'3.10'
    numpy>=1.19.2; platform_machine=='aarch64' and python_version<'3.10'
    numpy>=1.20.0; platform_machine=='arm64' and python_version<'3.10'
    numpy>=1.21.0; python_version>='3.10'
    python-dateutil>=2.8.1
    pytz>=2020.1
python_requires = >=3.8
include_package_data = True
zip_safe = False

[options.entry_points]
pandas_plotting_backends =
    matplotlib = pandas:plotting._matplotlib

[options.extras_require]
test =
    hypothesis>=5.5.3
    pytest>=6.0
    pytest-xdist>=1.31

[options.package_data]
* = templates/*, _libs/**/*.dll

[build_ext]
inplace = True

[options.packages.find]
include = pandas, pandas.*

# See the docstring in versioneer.py for instructions. Note that you must
# re-run 'versioneer.py setup' after changing this section, and commit the
# resulting files.
[versioneer]
VCS = git
style = pep440
versionfile_source = pandas/_version.py
versionfile_build = pandas/_version.py
tag_prefix = v
parentdir_prefix = pandas-

[flake8]
max-line-length = 88
ignore =
    # space before : (needed for how black formats slicing)
    E203,
    # line break before binary operator
    W503,
    # line break after binary operator
    W504,
    # module level import not at top of file
    E402,
    # do not assign a lambda expression, use a def
    E731,
    # found modulo formatter (incorrect picks up mod operations)
    S001,
    # controversial
    B005,
    # controversial
    B006,
    # controversial
    B007,
    # controversial
    B008,
    # setattr is used to side-step mypy
    B009,
    # getattr is used to side-step mypy
    B010,
    # tests use assert False
    B011,
    # tests use comparisons but not their returned value
    B015,
    # false positives
    B301,
    # single-letter variables
    PDF023
    # "use 'pandas._testing' instead" in non-test code
    PDF025
exclude =
    doc/sphinxext/*.py,
    doc/build/*.py,
    doc/temp/*.py,
    .eggs/*.py,
    versioneer.py,
    # exclude asv benchmark environments from linting
    env
per-file-ignores =
    # private import across modules
    pandas/tests/*:PDF020
    # pytest.raises without match=
    pandas/tests/extension/*:PDF009
    # os.remove
    doc/make.py:PDF008
    # import from pandas._testing
    pandas/testing.py:PDF014


[flake8-rst]
max-line-length = 84
bootstrap =
    import numpy as np
    import pandas as pd
    # avoiding error when importing again numpy or pandas
    np
    # (in some cases we want to do it to show users)
    pd
ignore =
    # space before : (needed for how black formats slicing)
    E203,
    # module level import not at top of file
    E402,
    # line break before binary operator
    W503,
    # Classes/functions in different blocks can generate those errors
    # expected 2 blank lines, found 0
    E302,
    # expected 2 blank lines after class or function definition, found 0
    E305,
    # We use semicolon at the end to avoid displaying plot objects
    # statement ends with a semicolon
    E703,
    # comparison to none should be 'if cond is none:'
    E711,
exclude =
    doc/source/development/contributing_docstring.rst,
    # work around issue of undefined variable warnings
    # https://github.com/pandas-dev/pandas/pull/38837#issuecomment-752884156
    doc/source/getting_started/comparison/includes/*.rst

[codespell]
ignore-words-list = ba,blocs,coo,hist,nd,sav,ser
ignore-regex = https://(\w+\.)+

[coverage:run]
branch = True
omit =
    */tests/*
    pandas/_typing.py
    pandas/_version.py
plugins = Cython.Coverage
source = pandas

[coverage:report]
ignore_errors = False
show_missing = True
omit =
    pandas/_version.py
# Regexes for lines to exclude from consideration
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover

    # Don't complain about missing debug-only code:
    def __repr__
    if self\.debug

    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError
    AbstractMethodError

    # Don't complain if non-runnable code isn't run:
    if 0:
    if __name__ == .__main__.:
    if TYPE_CHECKING:

[coverage:html]
directory = coverage_html_report
