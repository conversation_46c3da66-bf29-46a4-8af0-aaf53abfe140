[build-system]
# Minimum requirements for the build system to execute.
# See https://github.com/scipy/scipy/pull/12940 for the AIX issue.
requires = [
    "setuptools>=51.0.0",
    "wheel",
    "Cython>=0.29.24,<3",  # Note: sync with setup.py, environment.yml and asv.conf.json
    "oldest-supported-numpy>=0.10"
]
# uncomment to enable pep517 after versioneer problem is fixed.
# https://github.com/python-versioneer/python-versioneer/issues/193
# build-backend = "setuptools.build_meta"

[tool.black]
target-version = ['py38', 'py39']
exclude = '''
(
    asv_bench/env
  | \.egg
  | \.git
  | \.hg
  | \.mypy_cache
  | \.nox
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | setup.py
)
'''

[tool.pytest.ini_options]
# sync minversion with pyproject.toml & install.rst
minversion =  "6.0"
addopts = "--strict-data-files --strict-markers --capture=no --durations=30 --junitxml=test-data.xml"
xfail_strict = true
testpaths = "pandas"
doctest_optionflags = [
  "NORMALIZE_WHITESPACE",
  "IGNORE_EXCEPTION_DETAIL",
  "ELLIPSIS",
]
filterwarnings = [
  "error:Sparse:FutureWarning",
  "error:The SparseArray:FutureWarning",
]
junit_family = "xunit2"
markers = [
  "single_cpu: mark a test that should run on a single cpu only",
  "slow: mark a test as slow",
  "network: mark a test as network",
  "db: tests requiring a database (mysql or postgres)",
  "high_memory: mark a test as a high-memory only",
  "clipboard: mark a pd.read_clipboard test",
  "arm_slow: mark a test as slow for arm64 architecture",
  "arraymanager: mark a test to run with ArrayManager enabled",
]
asyncio_mode = "strict"

[tool.mypy]
# Import discovery
mypy_path = "typings"
files = ["pandas", "typings"]
namespace_packages = false
explicit_package_bases = false
ignore_missing_imports = true
follow_imports = "normal"
follow_imports_for_stubs = false
no_site_packages = false
no_silence_site_packages = false
# Platform configuration
python_version = "3.8"
platform = "linux-64"
# Disallow dynamic typing
disallow_any_unimported = false # TODO
disallow_any_expr = false # TODO
disallow_any_decorated = false # TODO
disallow_any_explicit = false # TODO
disallow_any_generics = false # TODO
disallow_subclassing_any = false # TODO
# Untyped definitions and calls
disallow_untyped_calls = false # TODO
disallow_untyped_defs = false # TODO
disallow_incomplete_defs = false # TODO
check_untyped_defs = true
disallow_untyped_decorators = true
# None and Optional handling
no_implicit_optional = true
strict_optional = true
# Configuring warnings
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_return_any = false # TODO
warn_unreachable = false # GH#27396
# Suppressing errors
show_none_errors = true
ignore_errors = false
# Miscellaneous strictness flags
allow_untyped_globals = false
allow_redefinition = false
local_partial_types = false
implicit_reexport = true
strict_equality = true
# Configuring error messages
show_error_context = false
show_column_numbers = false
show_error_codes = true

[[tool.mypy.overrides]]
module = [
  "pandas.tests.*",
  "pandas._version",
  "pandas.io.clipboard",
]
check_untyped_defs = false

[[tool.mypy.overrides]]
module = [
  "pandas.tests.apply.test_series_apply",
  "pandas.tests.arithmetic.conftest",
  "pandas.tests.arrays.sparse.test_combine_concat",
  "pandas.tests.dtypes.test_common",
  "pandas.tests.frame.methods.test_to_records",
  "pandas.tests.groupby.test_rank",
  "pandas.tests.groupby.transform.test_transform",
  "pandas.tests.indexes.interval.test_interval",
  "pandas.tests.indexing.test_categorical",
  "pandas.tests.io.excel.test_writers",
  "pandas.tests.reductions.test_reductions",
  "pandas.tests.test_expressions",
]
ignore_errors = true

# To be kept consistent with "Import Formatting" section in contributing.rst
[tool.isort]
known_pre_libs = "pandas._config"
known_pre_core = ["pandas._libs", "pandas._typing", "pandas.util._*", "pandas.compat", "pandas.errors"]
known_dtypes = "pandas.core.dtypes"
known_post_core = ["pandas.tseries", "pandas.io", "pandas.plotting"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY" ,"PRE_LIBS" , "PRE_CORE", "DTYPES", "FIRSTPARTY", "POST_CORE", "LOCALFOLDER"]
profile = "black"
combine_as_imports = true
force_grid_wrap = 2
force_sort_within_sections = true
skip_glob = "env"
skip = "pandas/__init__.py"

[tool.pyright]
pythonVersion = "3.8"
typeCheckingMode = "strict"
include = ["pandas", "typings"]
exclude = ["pandas/tests", "pandas/io/clipboard", "pandas/util/version"]
reportGeneralTypeIssues = false
reportConstantRedefinition = false
reportFunctionMemberAccess = false
reportImportCycles = false
reportIncompatibleMethodOverride = false
reportIncompatibleVariableOverride = false
reportMissingModuleSource = false
reportMissingParameterType = false
reportMissingTypeArgument = false
reportMissingTypeStubs = false
reportOptionalCall = false
reportOptionalIterable = false
reportOptionalMemberAccess = false
reportOptionalOperand = false
reportOptionalSubscript = false
reportPrivateImportUsage = false
reportPrivateUsage = false
reportUnboundVariable = false
reportUnknownArgumentType = false
reportUnknownLambdaType = false
reportUnknownMemberType = false
reportUnknownParameterType = false
reportUnknownVariableType = false
reportUnnecessaryComparison = false
reportUnnecessaryIsInstance = false
reportUnsupportedDunderAll = false
reportUntypedBaseClass = false
reportUnusedClass = false
reportUnusedFunction = false
reportUnusedImport = false
reportUnusedVariable = false
