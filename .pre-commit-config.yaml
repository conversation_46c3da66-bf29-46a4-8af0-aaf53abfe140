minimum_pre_commit_version: 2.9.2
exclude: ^LICENSES/|\.(html|csv|svg)$
ci:
    autofix_prs: false
repos:
-   repo: https://github.com/MarcoGorelli/absolufy-imports
    rev: v0.3.0
    hooks:
    -   id: absolufy-imports
        files: ^pandas/
-   repo: https://github.com/python/black
    rev: 22.3.0
    hooks:
    -   id: black
-   repo: https://github.com/codespell-project/codespell
    rev: v2.1.0
    hooks:
    -   id: codespell
        types_or: [python, rst, markdown]
        files: ^(pandas|doc)/
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.1.0
    hooks:
    -   id: debug-statements
    -   id: end-of-file-fixer
        exclude: \.txt$
    -   id: trailing-whitespace
-   repo: https://github.com/cpplint/cpplint
    rev: 1.5.5
    hooks:
    -   id: cpplint
        # We don't lint all C files because we don't want to lint any that are built
        # from Cython files nor do we want to lint C files that we didn't modify for
        # this particular codebase (e.g. src/headers, src/klib). However,
        # we can lint all header files since they aren't "generated" like C files are.
        exclude: ^pandas/_libs/src/(klib|headers)/
        args: [--quiet, '--extensions=c,h', '--headers=h', --recursive, '--filter=-readability/casting,-runtime/int,-build/include_subdir']
-   repo: https://github.com/PyCQA/flake8
    rev: 4.0.1
    hooks:
    -   id: flake8
        additional_dependencies: &flake8_dependencies
        - flake8==4.0.1
        - flake8-comprehensions==3.7.0
        - flake8-bugbear==21.3.2
        - pandas-dev-flaker==0.4.0
-   repo: https://github.com/PyCQA/isort
    rev: 5.10.1
    hooks:
    -   id: isort
-   repo: https://github.com/asottile/pyupgrade
    rev: v2.31.0
    hooks:
    -   id: pyupgrade
        args: [--py38-plus]
-   repo: https://github.com/pre-commit/pygrep-hooks
    rev: v1.9.0
    hooks:
      - id: rst-backticks
      - id: rst-directive-colons
        types: [text]  # overwrite types: [rst]
        types_or: [python, rst]
      - id: rst-inline-touching-normal
        types: [text]  # overwrite types: [rst]
        types_or: [python, rst]
-   repo: https://github.com/asottile/yesqa
    rev: v1.3.0
    hooks:
    -   id: yesqa
        additional_dependencies: *flake8_dependencies
-   repo: local
    hooks:
    -   id: pyright
        name: pyright
        entry: pyright
        language: node
        pass_filenames: false
        types: [python]
        stages: [manual]
        # note: keep version in sync with .github/workflows/ci.yml
        additional_dependencies: ['pyright@1.1.202']
-   repo: local
    hooks:
    -   id: flake8-rst
        name: flake8-rst
        description: Run flake8 on code snippets in docstrings or RST files
        language: python
        entry: flake8-rst
        types: [rst]
        args: [--filename=*.rst]
        additional_dependencies: [flake8-rst==0.7.0, flake8==3.7.9]
    -   id: unwanted-patterns
        name: Unwanted patterns
        language: pygrep
        entry: |
            (?x)
            # outdated annotation syntax, missing error codes
            \#\ type:\ (?!ignore)
            |\#\ type:\s?ignore(?!\[)

            # Incorrect code-block / IPython directives
            |\.\.\ code-block\ ::
            |\.\.\ ipython\ ::
            # directive should not have a space before ::
            |\.\.\ \w+\ ::

            # Check for deprecated messages without sphinx directive
            |(DEPRECATED|DEPRECATE|Deprecated)(:|,|\.)
        types_or: [python, cython, rst]
    -   id: cython-casting
        name: Check Cython casting is `<type>obj`, not `<type> obj`
        language: pygrep
        entry: '[a-zA-Z0-9*]> '
        files: (\.pyx|\.pxi.in)$
    -   id: incorrect-backticks
        name: Check for backticks incorrectly rendering because of missing spaces
        language: pygrep
        entry: '[a-zA-Z0-9]\`\`?[a-zA-Z0-9]'
        types: [rst]
        files: ^doc/source/
    -   id: seed-check-asv
        name: Check for unnecessary random seeds in asv benchmarks
        language: pygrep
        entry: 'np\.random\.seed'
        files: ^asv_bench/benchmarks
        exclude: ^asv_bench/benchmarks/pandas_vb_common\.py
    -   id: np-testing-array-equal
        name: Check for usage of numpy testing or array_equal
        language: pygrep
        entry: '(numpy|np)(\.testing|\.array_equal)'
        files: ^pandas/tests/
        types: [python]
    -   id: invalid-ea-testing
        name: Check for invalid EA testing
        language: pygrep
        entry: 'tm\.assert_(series|frame)_equal'
        files: ^pandas/tests/extension/base
        types: [python]
        exclude: ^pandas/tests/extension/base/base\.py
    -   id: pip-to-conda
        name: Generate pip dependency from conda
        description: This hook checks if the conda environment.yml and requirements-dev.txt are equal
        language: python
        entry: python scripts/generate_pip_deps_from_conda.py
        files: ^(environment.yml|requirements-dev.txt)$
        pass_filenames: false
        additional_dependencies: [pyyaml, toml]
    -   id: sync-flake8-versions
        name: Check flake8 version is synced across flake8, yesqa, and environment.yml
        language: python
        entry: python scripts/sync_flake8_versions.py
        files: ^(\.pre-commit-config\.yaml|environment\.yml)$
        pass_filenames: false
        additional_dependencies: [pyyaml]
    -   id: title-capitalization
        name: Validate correct capitalization among titles in documentation
        entry: python scripts/validate_rst_title_capitalization.py
        language: python
        types: [rst]
        files: ^doc/source/(development|reference)/
    -   id: use-pd_array-in-core
        name: Import pandas.array as pd_array in core
        language: python
        entry: python scripts/use_pd_array_in_core.py
        files: ^pandas/core/
        exclude: ^pandas/core/api\.py$
        types: [python]
    -   id: no-bool-in-core-generic
        name: Use bool_t instead of bool in pandas/core/generic.py
        entry: python scripts/no_bool_in_generic.py
        language: python
        files: ^pandas/core/generic\.py$
    -   id: pandas-errors-documented
        name: Ensure pandas errors are documented in doc/source/reference/general_utility_functions.rst
        entry: python scripts/pandas_errors_documented.py
        language: python
        files: ^pandas/errors/__init__.py$
    -   id: pg8000-not-installed-CI
        name: Check for pg8000 not installed on CI for test_pg8000_sqlalchemy_passthrough_error
        language: pygrep
        entry: 'pg8000'
        files: ^ci/deps
        types: [yaml]
    -   id: validate-min-versions-in-sync
        name: Check minimum version of dependencies are aligned
        entry: python scripts/validate_min_versions_in_sync.py
        language: python
        files: ^(ci/deps/actions-.*-minimum_versions\.yaml|pandas/compat/_optional\.py)$
