#########################################
# Editor temporary/working/backup files #
.#*
*\#*\#
[#]*#
*~
*$
*.bak
*flymake*
*.iml
*.kdev4
*.log
*.swp
*.pdb
*.zip
.project
.pydevproject
.settings
.idea
.vagrant
.noseids
.ipynb_checkpoints
.tags
.cache/
.vscode/

# Compiled source #
###################
*.a
*.com
*.class
*.dll
*.exe
*.pxi
*.o
*.py[ocd]
*.so
.build_cache_dir
MANIFEST

# Python files #
################
# setup.py working directory
build
# sphinx build directory
doc/_build
# setup.py dist directory
dist
# Egg metadata
*.egg-info
.eggs
.pypirc
# type checkers
pandas/py.typed

# tox testing tool
.tox
# rope
.ropeproject
# wheel files
*.whl
**/wheelhouse/*
pip-wheel-metadata
# coverage
.coverage
coverage.xml
coverage_html_report
.mypy_cache
*.pytest_cache
# hypothesis test database
.hypothesis/
__pycache__
# pytest-monkeytype
monkeytype.sqlite3


# OS generated files #
######################
.directory
.gdb_history
.DS_Store
ehthumbs.db
Icon?
Thumbs.db

# Data files #
##############
*.dta
*.xpt
*.h5
pandas/io/*.dat
pandas/io/*.json
scikits

# Generated Sources #
#####################
!skts.c
!np_datetime.c
!np_datetime_strings.c
*.c
*.cpp

# Unit / Performance Testing #
##############################
asv_bench/env/
asv_bench/html/
asv_bench/results/
asv_bench/pandas/
test-data.xml

# Documentation generated files #
#################################
doc/source/generated
doc/source/user_guide/styled.xlsx
doc/source/reference/api
doc/source/_static/*.html
doc/source/vbench
doc/source/vbench.rst
doc/source/index.rst
doc/build/html/index.html
# Windows specific leftover:
doc/tmp.sv
env/
doc/source/savefig/
