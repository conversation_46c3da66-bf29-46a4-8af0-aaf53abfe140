{
    // The version of the config file format.  Do not change, unless
    // you know what you are doing.
    "version": 1,

    // The name of the project being benchmarked
    "project": "pandas",

    // The project's homepage
    "project_url": "https://pandas.pydata.org/",

    // The URL of the source code repository for the project being
    // benchmarked
    "repo": "..",

    // List of branches to benchmark. If not provided, defaults to "master"
    // (for git) or "default" (for mercurial).
    "branches": ["main"],

    // The tool to use to create environments.  May be "conda",
    // "virtualenv" or other value depending on the plugins in use.
    // If missing or the empty string, the tool will be automatically
    // determined by looking for tools on the PATH environment
    // variable.
    "environment_type": "conda",

    // the base URL to show a commit for the project.
    "show_commit_url": "https://github.com/pandas-dev/pandas/commit/",

    // The Pythons you'd like to test against.  If not provided, defaults
    // to the current version of Python used to run `asv`.
    "pythons": ["3.8"],

    // The matrix of dependencies to test.  Each key is the name of a
    // package (in PyPI) and the values are version numbers.  An empty
    // list or empty string indicates to just test against the default
    // (latest) version. null indicates that the package is to not be
    // installed. If the package to be tested is only available from
    // PyPi, and the 'environment_type' is conda, then you can preface
    // the package name by 'pip+', and the package will be installed via
    // pip (with all the conda available packages installed first,
    // followed by the pip installed packages).
    "matrix": {
        "numpy": [],
        "Cython": ["0.29.24"],
        "matplotlib": [],
        "sqlalchemy": [],
        "scipy": [],
        "numba": [],
        "numexpr": [],
        "pytables": [null, ""],  // platform dependent, see excludes below
        "pyarrow": [],
        "tables": [null, ""],
        "openpyxl": [],
        "xlsxwriter": [],
        "xlrd": [],
        "xlwt": [],
        "odfpy": [],
        "jinja2": [],
    },
    "conda_channels": ["defaults", "conda-forge"],
    // Combinations of libraries/python versions can be excluded/included
    // from the set to test. Each entry is a dictionary containing additional
    // key-value pairs to include/exclude.
    //
    // An exclude entry excludes entries where all values match. The
    // values are regexps that should match the whole string.
    //
    // An include entry adds an environment. Only the packages listed
    // are installed. The 'python' key is required. The exclude rules
    // do not apply to includes.
    //
    // In addition to package names, the following keys are available:
    //
    // - python
    //     Python version, as in the *pythons* variable above.
    // - environment_type
    //     Environment type, as above.
    // - sys_platform
    //     Platform, as in sys.platform. Possible values for the common
    //     cases: 'linux2', 'win32', 'cygwin', 'darwin'.
    "exclude": [
        // On conda install pytables, otherwise tables
        {"environment_type": "conda", "tables": ""},
        {"environment_type": "conda", "pytables": null},
        {"environment_type": "(?!conda).*", "tables": null},
        {"environment_type": "(?!conda).*", "pytables": ""},
    ],
    "include": [],

    // The directory (relative to the current directory) that benchmarks are
    // stored in.  If not provided, defaults to "benchmarks"
    // "benchmark_dir": "benchmarks",

    // The directory (relative to the current directory) to cache the Python
    // environments in.  If not provided, defaults to "env"
    // "env_dir": "env",

    // The directory (relative to the current directory) that raw benchmark
    // results are stored in.  If not provided, defaults to "results".
    // "results_dir": "results",

    // The directory (relative to the current directory) that the html tree
    // should be written to.  If not provided, defaults to "html".
    // "html_dir": "html",

    // The number of characters to retain in the commit hashes.
    // "hash_length": 8,

    // `asv` will cache wheels of the recent builds in each
    // environment, making them faster to install next time.  This is
    // number of builds to keep, per environment.
    "build_cache_size": 8,

    // The commits after which the regression search in `asv publish`
    // should start looking for regressions. Dictionary whose keys are
    // regexps matching to benchmark names, and values corresponding to
    // the commit (exclusive) after which to start looking for
    // regressions.  The default is to start from the first commit
    // with results. If the commit is `null`, regression detection is
    // skipped for the matching benchmark.
    //
    "regressions_first_commits": {
        ".*": "0409521665"
    },
    "regression_thresholds": {
    },
    "build_command":
    ["python setup.py build -j4",
     "PIP_NO_BUILD_ISOLATION=false python -mpip wheel --no-deps --no-index -w {build_cache_dir} {build_dir}"],
}
