# ThriftInvoke 接口文档

## 接口概述

ThriftInvoke 是一个通用的 Thrift 服务调用接口，支持动态调用远程 Thrift 服务的方法。该接口提供了灵活的服务发现和直连两种调用方式。

## 接口信息

| 属性 | 值 |
|------|-----|
| **接口路径** | `/test/thriftInvoke` |
| **请求方法** | `POST` |
| **Content-Type** | `application/json` |
| **响应格式** | `text/plain;charset=UTF-8` |
| **响应内容** | JSON字符串 |

## 请求参数

### 请求体结构

```json
{
  "serviceInfo": "string",
  "paramsType": ["string"],
  "paramValues": ["string"]
}
```

### 参数详细说明

#### 1. serviceInfo
- **类型**: `String`
- **必填**: 是
- **说明**: Thrift服务信息的JSON字符串，包含服务连接和调用配置
- **格式**: JSON字符串，需要转义双引号

#### 2. paramsType
- **类型**: `List<String>`
- **必填**: 是
- **说明**: 方法参数类型列表，按顺序对应目标方法的参数类型
- **示例**: `["java.lang.String", "java.lang.Integer", "java.util.Map"]`

#### 3. paramValues
- **类型**: `List<String>`
- **必填**: 是
- **说明**: 方法参数值列表，按顺序对应目标方法的参数值
- **注意**: 所有参数值都以字符串形式传递，复杂对象需要序列化为JSON字符串

## serviceInfo 参数结构

serviceInfo 是一个JSON字符串，包含以下字段：

| 字段名 | 类型 | 必填 | 默认值 | 说明 | 示例 |
|--------|------|------|--------|------|------|
| `appKey` | String | 否 | - | 目标服务的应用标识，用于服务发现 | `"com.sankuai.user.service"` |
| `interfaceName` | String | 是 | - | Thrift接口的全限定类名 | `"com.sankuai.user.UserService"` |
| `methodName` | String | 是 | - | 要调用的方法名称 | `"getUserById"` |
| `cell` | String | 否 | - | 服务所在的机房标识 | `"bjdc"` |
| `ip` | String | 否 | - | 服务IP地址（直连模式） | `"**********"` |
| `port` | String | 否 | - | 服务端口（直连模式） | `"8080"` |
| `timeOut` | Integer | 否 | 6000 | 调用超时时间（毫秒） | `5000` |

### 连接方式说明

1. **服务发现模式**: 提供 `appKey`，系统自动发现服务实例
2. **直连模式**: 提供 `ip` 和 `port`，直接连接指定服务实例

## 调用示例

### 示例1: 服务发现模式调用用户服务

```bash
curl -X POST http://your-host/test/thriftInvoke \
  -H "Content-Type: application/json" \
  -d '{
    "serviceInfo": "{\"appKey\":\"com.sankuai.user.service\",\"interfaceName\":\"com.sankuai.user.UserService\",\"methodName\":\"getUserById\",\"cell\":\"bjdc\",\"timeOut\":5000}",
    "paramsType": ["java.lang.Long"],
    "paramValues": ["12345"]
  }'
```

### 示例2: 直连模式调用订单服务

```bash
curl -X POST http://your-host/test/thriftInvoke \
  -H "Content-Type: application/json" \
  -d '{
    "serviceInfo": "{\"interfaceName\":\"com.sankuai.order.OrderService\",\"methodName\":\"createOrder\",\"ip\":\"**********\",\"port\":\"8080\",\"timeOut\":3000}",
    "paramsType": ["java.lang.String", "java.lang.Double"],
    "paramValues": ["{\"userId\":123,\"productId\":456}", "99.99"]
  }'
```

### 示例3: 调用无参数方法

```bash
curl -X POST http://your-host/test/thriftInvoke \
  -H "Content-Type: application/json" \
  -d '{
    "serviceInfo": "{\"appKey\":\"com.sankuai.config.service\",\"interfaceName\":\"com.sankuai.config.ConfigService\",\"methodName\":\"getSystemConfig\",\"timeOut\":3000}",
    "paramsType": [],
    "paramValues": []
  }'
```

### 示例4: 调用复杂参数方法

```bash
curl -X POST http://your-host/test/thriftInvoke \
  -H "Content-Type: application/json" \
  -d '{
    "serviceInfo": "{\"appKey\":\"com.sankuai.payment.service\",\"interfaceName\":\"com.sankuai.payment.PaymentService\",\"methodName\":\"processPayment\"}",
    "paramsType": ["com.sankuai.payment.PaymentRequest", "java.util.Map"],
    "paramValues": ["{\"orderId\":\"ORD123\",\"amount\":100.50,\"currency\":\"CNY\"}", "{\"channel\":\"alipay\",\"async\":true}"]
  }'
```

## 响应格式

### 成功响应

接口返回目标Thrift服务方法的执行结果，以JSON字符串形式返回。

**示例**:
```json
"{\"code\":0,\"data\":{\"userId\":12345,\"userName\":\"张三\",\"email\":\"<EMAIL>\",\"status\":\"active\"}}"
```

### 异常响应

当调用失败时，接口返回 `"null"`。

**示例**:
```json
"null"
```

## 常见参数类型映射

| Java类型 | 示例值 | 说明 |
|----------|--------|------|
| `java.lang.String` | `"hello world"` | 字符串类型 |
| `java.lang.Integer` | `"123"` | 整数类型 |
| `java.lang.Long` | `"123456789"` | 长整数类型 |
| `java.lang.Double` | `"99.99"` | 双精度浮点数 |
| `java.lang.Boolean` | `"true"` | 布尔类型 |
| `java.util.List` | `"[\"item1\",\"item2\"]"` | 列表类型（JSON数组） |
| `java.util.Map` | `"{\"key1\":\"value1\",\"key2\":\"value2\"}"` | 映射类型（JSON对象） |
| 自定义对象 | `"{\"field1\":\"value1\",\"field2\":123}"` | 复杂对象（JSON对象） |

## 注意事项

### 1. 参数格式要求
- `serviceInfo` 必须是有效的JSON字符串，注意转义双引号
- `paramsType` 和 `paramValues` 数组长度必须一致
- 参数类型必须与目标方法签名完全匹配

### 2. 连接配置
- 推荐使用服务发现模式（appKey），更加稳定可靠
- 直连模式适用于测试环境或特殊场景
- 合理设置超时时间，避免长时间等待

### 3. 错误处理
- 接口调用失败时返回 `"null"`
- 建议调用方实现重试机制和降级策略
- 注意处理网络异常和服务不可用情况

### 4. 性能考虑
- 该接口会创建和缓存Thrift客户端连接
- 相同服务配置的连接会被复用
- 避免频繁创建不同配置的连接

## 错误排查

### 常见错误及解决方案

1. **返回 "null"**
   - 检查 serviceInfo 格式是否正确
   - 验证目标服务是否可达
   - 确认方法名和参数类型是否匹配

2. **连接超时**
   - 检查网络连通性
   - 适当增加 timeOut 值
   - 确认目标服务是否正常运行

3. **参数类型错误**
   - 确保 paramsType 中的类型名称正确
   - 检查参数值格式是否符合要求
   - 验证复杂对象的JSON格式

## 版本信息

- **文档版本**: v1.0
- **接口版本**: 当前版本
- **最后更新**: 2024年12月

---

> **提示**: 使用该接口前，请确保目标Thrift服务已正常部署并可访问。建议在测试环境充分验证后再在生产环境使用。

