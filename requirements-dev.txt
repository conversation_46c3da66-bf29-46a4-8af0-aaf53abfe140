# This file is auto-generated from environment.yml, do not modify.
# See that file for comments about the need/usage of each dependency.

numpy>=1.18.5
python-dateutil>=2.8.1
pytz
asv < 0.5.0
cython>=0.29.24
black==22.3.0
cpplint
flake8==4.0.1
flake8-bugbear==21.3.2
flake8-comprehensions==3.7.0
isort>=5.2.1
mypy==0.930
pre-commit>=2.9.2
pycodestyle
pyupgrade
gitpython
gitdb
numpydoc < 1.2
pandas-dev-flaker==0.4.0
pydata-sphinx-theme
pytest-cython
sphinx
sphinx-panels
types-python-dateutil
types-PyMySQL
types-pytz
types-setuptools
nbconvert>=5.4.1
nbsphinx
pandoc
dask
toolz>=0.7.3
partd>=0.3.10
cloudpickle>=0.2.1
markdown
feedparser
pyyaml
requests
boto3
botocore>=1.11
hypothesis>=5.5.3
moto
flask
pytest>=6.0
pytest-cov
pytest-xdist>=1.31
pytest-asyncio>=0.17
pytest-instafail
seaborn
statsmodels
ipywidgets
nbformat
notebook>=6.0.3
blosc
bottleneck>=1.3.1
ipykernel
ipython>=7.11.1
jinja2<=3.0.3
matplotlib>=3.3.2
numexpr>=2.7.1
scipy>=1.4.1
numba>=0.50.1
beautifulsoup4>=4.8.2
html5lib
lxml
openpyxl
xlrd
xlsxwriter
xlwt
odfpy
fastparquet>=0.4.0
pyarrow>2.0.1
python-snappy
tables>=3.6.1
s3fs>=0.4.0
aiobotocore<2.0.0
fsspec>=0.7.4
gcsfs>=0.6.0
sqlalchemy
xarray<0.19
cftime
pyreadstat
tabulate>=0.8.3
natsort
setuptools>=51.0.0
