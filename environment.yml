name: pandas-dev
channels:
  - conda-forge
dependencies:
  # required
  - numpy>=1.18.5
  - python=3.8
  - python-dateutil>=2.8.1
  - pytz

  # benchmarks
  - asv < 0.5.0  # 2022-02-08: v0.5.0 > leads to ASV checks running > 3 hours on CI

  # building
  # The compiler packages are meta-packages and install the correct compiler (activation) packages on the respective platforms.
  - c-compiler
  - cxx-compiler
  - cython>=0.29.24

  # code checks
  - black=22.3.0
  - cpplint
  - flake8=4.0.1
  - flake8-bugbear=21.3.2  # used by flake8, find likely bugs
  - flake8-comprehensions=3.7.0  # used by flake8, linting of unnecessary comprehensions
  - isort>=5.2.1  # check that imports are in the right order
  - mypy=0.930
  - pre-commit>=2.9.2
  - pycodestyle  # used by flake8
  - pyupgrade

  # documentation
  - gitpython  # obtain contributors from git for whatsnew
  - gitdb
  - numpydoc < 1.2  # 2021-02-09 1.2dev breaking CI
  - pandas-dev-flaker=0.4.0
  - pydata-sphinx-theme
  - pytest-cython
  - sphinx
  - sphinx-panels
  - types-python-dateutil
  - types-PyMySQL
  - types-pytz
  - types-setuptools

  # documentation (jupyter notebooks)
  - nbconvert>=5.4.1
  - nbsphinx
  - pandoc

  # Dask and its dependencies (that dont install with dask)
  - dask-core
  - toolz>=0.7.3
  - partd>=0.3.10
  - cloudpickle>=0.2.1

  # web (jinja2 is also needed, but it's also an optional pandas dependency)
  - markdown
  - feedparser
  - pyyaml
  - requests

  # testing
  - boto3
  - botocore>=1.11
  - hypothesis>=5.5.3
  - moto  # mock S3
  - flask
  - pytest>=6.0
  - pytest-cov
  - pytest-xdist>=1.31
  - pytest-asyncio>=0.17
  - pytest-instafail

  # downstream tests
  - seaborn
  - statsmodels

  # unused (required indirectly may be?)
  - ipywidgets
  - nbformat
  - notebook>=6.0.3

  # optional
  - blosc
  - bottleneck>=1.3.1
  - ipykernel
  - ipython>=7.11.1
  - jinja2<=3.0.3  # pandas.Styler
  - matplotlib>=3.3.2  # pandas.plotting, Series.plot, DataFrame.plot
  - numexpr>=2.7.1
  - scipy>=1.4.1
  - numba>=0.50.1

  # optional for io
  # ---------------
  # pd.read_html
  - beautifulsoup4>=4.8.2
  - html5lib
  - lxml

  # pd.read_excel, DataFrame.to_excel, pd.ExcelWriter, pd.ExcelFile
  - openpyxl
  - xlrd
  - xlsxwriter
  - xlwt
  - odfpy

  - fastparquet>=0.4.0  # pandas.read_parquet, DataFrame.to_parquet
  - pyarrow>2.0.1  # pandas.read_parquet, DataFrame.to_parquet, pandas.read_feather, DataFrame.to_feather
  - python-snappy  # required by pyarrow

  - pytables>=3.6.1  # pandas.read_hdf, DataFrame.to_hdf
  - s3fs>=0.4.0  # file IO when using 's3://...' path
  - aiobotocore<2.0.0  # GH#44311 pinned to fix docbuild
  - fsspec>=0.7.4  # for generic remote file operations
  - gcsfs>=0.6.0  # file IO when using 'gcs://...' path
  - sqlalchemy  # pandas.read_sql, DataFrame.to_sql
  - xarray<0.19  # DataFrame.to_xarray
  - cftime  # Needed for downstream xarray.CFTimeIndex test
  - pyreadstat  # pandas.read_spss
  - tabulate>=0.8.3  # DataFrame.to_markdown
  - natsort  # DataFrame.sort_values
